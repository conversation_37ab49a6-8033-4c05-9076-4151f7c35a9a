{"expo": {"name": "Bolaoo", "slug": "TOTOLOCO", "version": "1.1.2", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "totoloco", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.shunyasoftkd.TOTOLOCO", "buildNumber": "6", "usesAppleSignIn": true, "entitlements": {"com.apple.developer.applesignin": ["<PERSON><PERSON><PERSON>"]}, "infoPlist": {"UIBackgroundModes": ["fetch", "remote-notification"], "NSLocationWhenInUseUsageDescription": "Bolaoo needs your location to show nearby available rides and calculate accurate pickup positions. This helps us connect you with the closest drivers and provide accurate fare estimates.", "NSUserNotificationUsageDescription": "We'll send you important notifications about your ride status, driver arrival, booking confirmations, and essential service updates to ensure a smooth ride experience.", "NSUserNotificationsEnabled": true, "ITSAppUsesNonExemptEncryption": false, "LSApplicationQueriesSchemes": ["tel", "mailto", "whatsapp"]}}, "android": {"versionCode": 11, "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#128756"}, "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.POST_NOTIFICATIONS", "android.permission.WAKE_LOCK", "android.permission.FOREGROUND_SERVICE", "android.permission.FOREGROUND_SERVICE_LOCATION", "android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.VIBRATE"], "package": "com.shunyasoftkd.TOTOLOCO", "googleServicesFile": "./google-services.json"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#128756"}], ["expo-location", {"locationWhenInUsePermission": "Bolaoo needs your location to show nearby available rides and calculate accurate pickup positions. This helps us connect you with the closest drivers and provide accurate fare estimates."}], "expo-font", ["expo-task-manager", {"experimentsEnabled": true}], ["@sentry/react-native", {"organization": "shunyasoft", "project": "react-native", "enableInExpoDevelopment": true}], ["expo-build-properties", {"android": {"foregroundService": true, "usesCleartextTraffic": true, "targetSdkVersion": 34}}], ["expo-notifications", {"icon": "./assets/images/favicon.png", "color": "#ffffff", "defaultChannel": "default", "sounds": ["./assets/sounds/notification_sound.wav"], "mode": "production"}], "expo-localization", "expo-secure-store"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "68348f4c-5b1c-484f-b592-d9115fdadf4c"}}, "runtimeVersion": "1.0.6", "updates": {"url": "https://u.expo.dev/68348f4c-5b1c-484f-b592-d9115fdadf4c"}}}