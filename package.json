{"name": "bolaoo", "main": "expo-router/entry", "version": "1.1.2", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "version:patch": "npm version patch && node scripts/update-build-numbers.js", "version:minor": "npm version minor && node scripts/update-build-numbers.js", "version:major": "npm version major && node scripts/update-build-numbers.js", "format": "\"prettier --write .\""}, "jest": {"preset": "jest-expo"}, "dependencies": {"@apollo/client": "^3.12.4", "@expo-google-fonts/poppins": "^0.2.3", "@expo/vector-icons": "^14.0.2", "@nhost/nhost-js": "^3.2.2", "@nhost/react": "^3.8.1", "@nhost/react-apollo": "^15.0.1", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/netinfo": "11.4.1", "@react-navigation/bottom-tabs": "^7.0.0", "@react-navigation/native": "^7.0.0", "@sentry/react-native": "~6.10.0", "@shopify/flash-list": "1.7.3", "axios": "^1.7.8", "date-fns": "^4.1.0", "expo": "~52.0.46", "expo-apple-authentication": "~7.1.3", "expo-battery": "~9.0.2", "expo-build-properties": "~0.13.3", "expo-constants": "~17.0.3", "expo-dev-client": "~5.0.19", "expo-device": "~7.0.3", "expo-document-picker": "~13.0.3", "expo-font": "~13.0.1", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-localization": "~16.0.1", "expo-location": "~18.0.10", "expo-notifications": "~0.29.14", "expo-router": "~4.0.21", "expo-secure-store": "~14.0.1", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-task-manager": "~12.0.6", "expo-updates": "~0.27.4", "expo-web-browser": "~14.0.2", "graphql": "^16.9.0", "graphql-ws": "^5.16.0", "i18next": "^24.1.2", "react": "18.3.1", "react-dom": "18.3.1", "react-i18next": "^15.2.0", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-otp-textinput": "^1.1.6", "react-native-paper": "^5.12.5", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "semver": "^7.7.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/prettier": "^2.7.3", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "@types/semver": "^7.5.8", "jest": "^29.2.1", "jest-expo": "~52.0.6", "prettier": "^3.4.2", "react-test-renderer": "18.3.1", "reactotron-react-native": "^5.1.12", "typescript": "^5.3.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}, "private": true}