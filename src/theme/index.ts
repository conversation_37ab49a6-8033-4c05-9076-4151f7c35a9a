import { MD3DarkTheme, MD3LightTheme, MD3Theme } from 'react-native-paper';
import { COLORS } from '../constants';

export const lightTheme: MD3Theme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: COLORS.primary,
    primaryContainer: COLORS.primary,
    secondary: COLORS.secondary,
    secondaryContainer: COLORS.secondary,
    tertiary: COLORS.accent,
    tertiaryContainer: COLORS.accent,
    surface: COLORS.surface,
    surfaceVariant: COLORS.surface,
    background: COLORS.background,
    error: COLORS.error,
    onPrimary: '#FFFFFF',
    onPrimaryContainer: '#FFFFFF',
    onSecondary: '#FFFFFF',
    onSecondaryContainer: '#FFFFFF',
    onTertiary: '#FFFFFF',
    onTertiaryContainer: '#FFFFFF',
    onSurface: COLORS.text,
    onSurfaceVariant: COLORS.text,
    onSurfaceDisabled: '#9E9E9E',
    onBackground: COLORS.text,
    onError: '#FFFFFF',
    outline: '#E0E0E0',
    outlineVariant: '#BDBDBD',
    scrim: 'rgba(0, 0, 0, 0.3)',
    inverseSurface: '#121212',
    inverseOnSurface: '#FFFFFF',
    inversePrimary: COLORS.primary,
    elevation: {
      level0: 'transparent',
      level1: '#F5F5F5',
      level2: '#EEEEEE',
      level3: '#E0E0E0',
      level4: '#BDBDBD',
      level5: '#9E9E9E',
    },
  },
};

export const darkTheme: MD3Theme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: COLORS.primary,
    primaryContainer: COLORS.primary,
    secondary: COLORS.secondary,
    secondaryContainer: COLORS.secondary,
    tertiary: COLORS.accent,
    tertiaryContainer: COLORS.accent,
    surface: '#1E1E1E',
    surfaceVariant: '#2C2C2C',
    background: '#121212',
    error: COLORS.error,
    onPrimary: '#FFFFFF',
    onPrimaryContainer: '#FFFFFF',
    onSecondary: '#FFFFFF',
    onSecondaryContainer: '#FFFFFF',
    onTertiary: '#FFFFFF',
    onTertiaryContainer: '#FFFFFF',
    onSurface: '#FFFFFF',
    onSurfaceVariant: '#CCCCCC',
    onSurfaceDisabled: '#666666',
    onBackground: '#FFFFFF',
    onError: '#FFFFFF',
    outline: '#404040',
    outlineVariant: '#2C2C2C',
    scrim: 'rgba(0, 0, 0, 0.6)',
    inverseSurface: '#FFFFFF',
    inverseOnSurface: '#121212',
    inversePrimary: COLORS.primary,
    elevation: {
      level0: 'transparent',
      level1: '#1E1E1E',
      level2: '#222222',
      level3: '#252525',
      level4: '#272727',
      level5: '#2C2C2C',
    },
  },
};

export type AppTheme = typeof lightTheme;
