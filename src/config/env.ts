import developmentEnv from './env.development';
import productionEnv from './env.production';

// Type for our environment variables
export type Environment = typeof developmentEnv;

// Get the correct environment based on __DEV__
const env: Environment = __DEV__ ? developmentEnv : productionEnv;

// Validate that we have all required environment variables
const validateEnv = (environment: Environment) => {
  const required = [
    'NHOST',
    'API_URL',
    'ENVIRONMENT',
    'LOCATIONIQ_KEY',
    'EXPO_PROJECT_ID',
    'DSN',
  ] as const;
  for (const key of required) {
    if (!(key in environment)) {
      throw new Error(`Missing required environment variable: ${key}`);
    }
  }
  return environment;
};

// Export validated environment
export default validateEnv(env);
