// App-wide constants
// Colors
export const COLORS = {
  // primary: '#149863',
  // primary: '#43A047',
  primary: '#128756',
  secondary: '#FFB100',
  accent: '#ff662a',
  // background: '#E0F2F1',
  // background: '#D8EFE6',
  // background: '#ECF0F1',
  // background: '#ECF0F1',
  // background: '#F5F5F5',
  // background: '#FFF8E1',
  // background: '#FFFFFF',
  background: '#D8EFE6',
  surface: '#FFFFFF',
  text: '#666666',
  error: '#D32F2F',
  success: '#2E7D32', // Green color for verified status
  warning: '#F57C00', // Orange color for not verified status
  disabled: '#90A4AE',
  divider: '#B0BEC5',
  blue: '#0000FF', // Added blue color
};

// Support Information
export const SUPPORT_PHONE = '+9779705801400';
export const SUPPORT_EMAIL = '<EMAIL>';
export const SUPPORT_WHATSAPP = '+9779705801400';

export const LINKS = {
  passengerApp: 'https://play.google.com/store/apps/details?id=com.shunyasoftkd.TOTOLOCO',
  driverApp: 'https://play.google.com/store/apps/details?id=com.shunyasoft.drivertotoloco',
  iosApp: 'https://apps.apple.com/nz/app/bolaoo/id6741805103',
  social: {
    facebook: 'https://facebook.com/BolaooNepal',
    instagram: 'https://www.instagram.com/bolaoonepal',
    twitter: 'https://X.com/BolaooNepal',
    tiktok: 'https://www.tiktok.com/@bolaoonepal',
    whatsapp: 'https://wa.me/9779705801400',
    threads: 'https://www.thread.com/bolaoonepal',
    youtube: 'https://www.youtube.com/@BolaooNepal',
  },
};

// App Configuration
export const SESSION_TIMEOUT = 1500; // milliseconds

// App configuration
export const APP_NAME = 'Bolaoo';
export const APP_VERSION = '1.0.0';

// API endpoints
export const API_URL = 'https://api.totoloco.com'; // Replace with actual API URL
export const GRAPHQL_URL = 'https://graphql.totoloco.com'; // Replace with actual GraphQL URL

// Storage keys
export const STORAGE_KEYS = {
  THEME: '@theme',
  AUTH_TOKEN: '@auth_token',
  USER_PREFERENCES: '@user_preferences',
};

// Default values
export const DEFAULT_LOCALE = 'en';
export const DEFAULT_CURRENCY = 'USD';
export const DEFAULT_TIMEZONE = 'UTC';

// Feature flags
export const FEATURES = {
  DARK_MODE: true,
  NOTIFICATIONS: true,
  LOCATION_TRACKING: true,
  OFFLINE_MODE: true,
};

// Time constants (in milliseconds)
export const TIME = {
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
};

// Animation durations
export const ANIMATION = {
  FAST: 200,
  NORMAL: 300,
  SLOW: 500,
};

// Default export to satisfy Expo Router
export default function Constants() {
  return null;
}

export const LOCATION_TASK_NAME = 'background-location-task';
export const LOCATION_STORAGE_KEY = '@location_history';
export const updateInterval = 5000;

export const LOCATIONIQ_BASE_URL = 'https://us1.locationiq.com/v1';

export const EXPO_NOTIFICATION_BASE_URL = 'https://exp.host/--/api/v2/push/send';
