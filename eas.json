{"build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"gradleCommand": ":app:assembleDebug"}, "ios": {"buildConfiguration": "Debug", "simulator": true}, "env": {"APP_ENV": "development"}, "channel": "development"}, "preview": {"distribution": "internal", "android": {"gradleCommand": ":app:assembleRelease"}, "ios": {"buildConfiguration": "Release"}, "env": {"APP_ENV": "staging"}, "channel": "preview"}, "production-1": {"distribution": "store", "android": {"gradleCommand": ":app:assembleRelease"}, "ios": {"buildConfiguration": "Release"}, "env": {"APP_ENV": "production"}, "channel": "production-1"}, "production": {"distribution": "store", "android": {"buildType": "app-bundle"}, "ios": {"buildConfiguration": "Release"}, "env": {"APP_ENV": "production"}, "channel": "production"}}, "submit": {"production": {}}}