import Reactotron from 'reactotron-react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

Reactotron.setAsyncStorageHandler(AsyncStorage)
  .configure({
    name: 'React Native Demo',
  })
  .useReactNative({
    asyncStorage: false, // Disable AsyncStorage tracking
    networking: {
      ignoreUrls: /symbolicate|127.0.0.1/, // Ignore specific URLs to reduce noise
    },
    editor: false, // Disable editor tracking
    errors: { veto: stackFrame => false }, // Enable error tracking
    overlay: false, // Disable overlay
  })
  .connect();
