const fs = require('fs');
const path = require('path');

// Read app.json
const appJsonPath = path.join(__dirname, '..', 'app.json');
const appJson = require(appJsonPath);

// Increment build numbers
if (appJson.expo.ios) {
  appJson.expo.ios.buildNumber = String(parseInt(appJson.expo.ios.buildNumber || "0") + 1);
}

if (appJson.expo.android) {
  appJson.expo.android.versionCode = (appJson.expo.android.versionCode || 0) + 1;
}

// Write back to app.json
fs.writeFileSync(appJsonPath, JSON.stringify(appJson, null, 2));
