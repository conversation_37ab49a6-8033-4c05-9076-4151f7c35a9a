# Bolaoo Driver App 🚗

A professional ride-sharing platform built with Expo and React Native, designed for drivers to manage rides, track earnings, and provide excellent service to passengers.

## Features 🌟

- **Real-time Ride Tracking**: Live GPS tracking and route optimization
- **Multi-language Support**: Localized for multiple regions
- **Document Management**: Easy upload and verification of driver documents
- **Earnings Dashboard**: Track your earnings and payment history
- **24/7 Support**: Direct access to customer support through multiple channels
- **Smart Notifications**: Real-time alerts for new ride requests and updates

## Tech Stack 💻

- Expo SDK 52
- React Native
- TypeScript
- Apollo Client for GraphQL
- Nhost Backend
- React Native Paper (UI Components)
- Sentry (Error Tracking)
- Expo Router (File-based Navigation)

## Getting Started 🚀

### Prerequisites

- Node.js (v18 or newer)
- npm or yarn
- Expo CLI
- iOS Simulator or Android Emulator

### Installation

1. Clone the repository:

   ```bash
   git clone [your-repository-url]
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Start the development server:

   ```bash
   npx expo start
   ```

4. Choose your platform:
   - Press `a` for Android
   - Press `i` for iOS
   - Press `w` for web

### Environment Setup

1. Create environment files:

   - `src/config/env.development.ts` for development
   - `src/config/env.production.ts` for production

2. Configure your environment variables following the template in `src/config/env.ts`

## Building for Production 🏗️

### Android

```bash
eas build --platform android --profile production-1
```

### iOS

```bash
eas build --platform ios --profile production-1
```

## Development Guidelines 📝

- Follow the TypeScript conventions
- Use React Native Paper components for UI consistency
- Implement error handling with Sentry
- Test on both Android and iOS platforms

## Available Scripts 📜

- `npm start`: Start the Expo development server
- `npm run android`: Run on Android
- `npm run ios`: Run on iOS
- `npm run web`: Run on web browser
- `npm run lint`: Run ESLint
- `npm run format`: Format code with Prettier
- `npm run version:patch`: Bump patch version
- `npm run version:minor`: Bump minor version
- `npm run version:major`: Bump major version

## Support 🤝

For support, please contact:

- WhatsApp: [Support Number]
- Email: [Support Email]
- In-app support section

## Contributing 🌱

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License 📄

[Your License Type] - See LICENSE file for details

## Version History 📌

- 1.0.8 - Current stable release
  - Enhanced location tracking
  - Improved document management
  - Bug fixes and performance improvements

Made with ♥ in Jhapa


to run android emulator in ios 


``` 
emulator -list-avds
emulator -avd Medium_Phone_API_36.0

```