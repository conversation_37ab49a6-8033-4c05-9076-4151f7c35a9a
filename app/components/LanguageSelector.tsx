import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import { List, RadioButton, Surface } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { changeLanguage, getCurrentLanguage } from '../utils/i18n';
import { useTheme } from '@/theme/ThemeContext';

const LanguageSelector = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [selectedLanguage, setSelectedLanguage] = useState(getCurrentLanguage());

  const handleLanguageChange = async (language: string) => {
    setSelectedLanguage(language);
    await changeLanguage(language);
  };

  const languages = [
    { code: 'en', name: t('languages.english') },
    { code: 'ne', name: t('languages.nepali') }
  ];

  return (
    <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
      <List.Section>
        <List.Subheader style={{ color: theme.colors.onSurfaceVariant }}>
          {t('settings.language')}
        </List.Subheader>
        <RadioButton.Group 
          onValueChange={handleLanguageChange} 
          value={selectedLanguage}
        >
          {languages.map((lang) => (
            <List.Item
              key={lang.code}
              title={lang.name}
              onPress={() => handleLanguageChange(lang.code)}
              right={() => (
                <RadioButton 
                  value={lang.code} 
                  color={theme.colors.primary}
                  uncheckedColor={theme.colors.onSurfaceVariant}
                />
              )}
            />
          ))}
        </RadioButton.Group>
      </List.Section>
    </Surface>
  );
};

export default LanguageSelector;

const styles = StyleSheet.create({
  section: {
    marginVertical: 12,
    borderRadius: 12,
    // overflow: 'hidden',
  },
});
