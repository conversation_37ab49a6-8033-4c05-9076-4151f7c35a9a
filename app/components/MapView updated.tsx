import React, { useEffect, useRef } from 'react';
import { StyleSheet, TouchableOpacity, View, Text } from 'react-native';
import { WebView } from 'react-native-webview';
import useLocationServices from '../hooks/useLocationServices';
// import useDriverLocations, { DriverLocation } from '../hooks/useDriverLocations';

const MapView = React.memo(
  ({ initialLocation = INITIAL_LOCATION, onCabSelect }: MapViewProps) => {
    const webViewRef = useRef<WebView>(null);
    const {
      currentLocation,
      // isBackgroundTracking,
      startForegroundTracking,
    } = useLocationServices();

 
    // Start foreground tracking on mount
    useEffect(() => {
      const setupTracking = async () => {
        await startForegroundTracking();
      };
      setupTracking();
    }, [startForegroundTracking]);

    // Update map when location changes
    useEffect(() => {
      if (currentLocation && webViewRef.current) {
        const userLocation = {
          latitude: currentLocation.coords.latitude,
          longitude: currentLocation.coords.longitude,
          heading: currentLocation.coords.heading || 0
        };

        webViewRef.current.injectJavaScript(`
          window.handleMessage(JSON.stringify({
            type: 'location',
            data: ${JSON.stringify(userLocation)}
          }));
          true;
        `);
      }
    }, [currentLocation]);

    // Update driver locations
    // useEffect(() => {
    //   if (webViewRef.current) {
    //     webViewRef.current.injectJavaScript(`
    //       window.handleMessage(JSON.stringify({
    //         type: 'cabs',
    //         data: { cabs: ${JSON.stringify(driverLocations)} }
    //       }));
    //       true;
    //     `);
    //   }
    // }, [driverLocations]);

    const coords = currentLocation?.coords || initialLocation;


    return (
      <View style={styles.container}>
       
        <WebView
          ref={webViewRef}
          source={{ html: htmlContent({ initialLocation: coords, onCabSelect }) }}
          style={styles.map}
          scrollEnabled={false}
          bounces={false}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          androidLayerType="hardware"
          renderToHardwareTextureAndroid
          javaScriptEnabled
          domStorageEnabled
          onMessage={(event) => {
            try {
              const data = JSON.parse(event.nativeEvent.data);
              if (data.type === 'cabSelected' && onCabSelect) {
                onCabSelect(data.cab);
              }
            } catch (e) {
              console.error('Error parsing message:', e);
            }
          }}
        />
      </View>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.initialLocation === nextProps.initialLocation &&
      prevProps.onCabSelect === nextProps.onCabSelect
    );
  }
);

export default MapView;

interface MapViewProps {
  initialLocation?: { latitude: number; longitude: number };
  onCabSelect?: (cab: any) => void;
}

const INITIAL_LOCATION = {
  latitude: 0,
  longitude: 0,
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  map: {
    flex: 1,
  },
});

const htmlContent = (props: MapViewProps) => `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <style>
        * {
            margin: 0;
            padding: 0;
            -webkit-user-select: none;
            user-select: none;
        }
        html, body, #map {
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: #f8f9fa;
        }
        .bike-marker {
            width: 36px;
            height: 36px;
            will-change: transform;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .cab-marker {
            font-size: 32px;  
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            width: 36px;
            height: 36px;
        }
        .driver-icon {
            transform-origin: center;
            transition: transform 0.3s ease;
        }
        .leaflet-marker-icon {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
    </style>
</head>
<body>
    <div id="map"></div>
    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
    <script>
        let map;
        let userMarker;
        let isMapInitialized = false;
        let lastUpdate = 0;
        const UPDATE_THRESHOLD = 16; // ~60fps

        const bikeIcon = L.divIcon({
            className: 'bike-marker',
            html: '<div class="cab-marker">' +
                  '<div class="driver-icon">🛺</div>' +
                  '</div>',
            iconSize: [60, 60],
            iconAnchor: [30, 30]
        });

        function initializeMap(location) {
            if (!isMapInitialized) {
                map = L.map('map', {
                    zoomControl: false,
                    attributionControl: false,
                    fadeAnimation: true,
                    zoomAnimation: true,
                    markerZoomAnimation: false,
                    preferCanvas: true,
                    renderer: L.canvas({ tolerance: 5 })
                }).setView([location.latitude, location.longitude], 17);
                
                L.tileLayer('https://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}&scale=3', {
                    maxZoom: 20,
                    minZoom: 10,
                    subdomains: ['mt0', 'mt1', 'mt2', 'mt3'],
                    tileSize: 256,
                    updateWhenIdle: true,
                    keepBuffer: 2
                }).addTo(map);

                userMarker = L.marker([location.latitude, location.longitude], {
                    icon: bikeIcon,
                    zIndexOffset: 1000,
                }).addTo(map);

                isMapInitialized = true;
            }
        }

        function updateLocation(location) {
            const now = Date.now();
            if (now - lastUpdate < UPDATE_THRESHOLD) return;
            lastUpdate = now;

            if (!isMapInitialized) {
                initializeMap(location);
                return;
            }

            const pos = [location.latitude, location.longitude];
            userMarker.setLatLng(pos);
            
            // Update bike rotation based on heading
            if (location.heading !== undefined) {
                const bikeElement = userMarker.getElement();
                if (bikeElement) {
                    const iconElement = bikeElement.querySelector('.driver-icon');
                    if (iconElement) {
                        // Adjust heading by 90 degrees to point in the direction of travel
                        iconElement.style.transform = 'rotate(' + (location.heading + 90) + 'deg)';
                    }
                }
            }

            map.panTo(pos, { animate: true, duration: 0.5 });
        }

        window.handleMessage = function(data) {
            try {
                const message = JSON.parse(data);
                if (message.type === 'location') {
                    updateLocation(message.data);
                }
            } catch (e) {
                console.error('Error in handleMessage:', e);
            }
        };
    </script>
</body>
</html>
`;
