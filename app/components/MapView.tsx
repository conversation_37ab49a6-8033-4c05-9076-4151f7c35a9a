import React, { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import { StyleSheet, View, Linking, TouchableOpacity, Platform, Modal } from 'react-native';
import { WebView } from 'react-native-webview';
import useLocationServices from '../hooks/useLocationServices';
import useDriverLocations from 'app/hooks/useDriverLocations';
import { useIsFocused } from '@react-navigation/native';
import {
  Surface,
  Title,
  FAB,
  Text,
  Button,
  RadioButton,
  TextInput,
  ActivityIndicator,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useMutation, useQuery, useSubscription } from '@apollo/client';
import {
  SUBSCRIBE_TO_ACTIVE_FREE_RIDE_REQUESTS,
  UPDATE_FREE_RIDE_REQUEST,
} from 'app/graphql/nearby';
import { useTheme } from '@/theme/ThemeContext';
import { useTranslation } from 'react-i18next';
import { UPDATE_DRIVER } from 'app/graphql/driver';
import { CREATE_RATING } from 'app/graphql/rating';
import { GET_FREE_RIDE_PK } from 'app/graphql/ride';
import NearbyVechicleCard from './NearbyVechicleCard';
import { sendtoDriverNotification } from 'app/utils/notification-sender';
import { useNotifications } from 'app/contexts/NotificationContext';
import AnimatedLoadingScreen from './AnimatedLoadingScreen';

interface MapViewProps {
  initialLocation?: { latitude: number; longitude: number };
  latitude: number;
  longitude: number;
  onCabSelect?: (cab: any) => void;
}

const INITIAL_LOCATION = {
  latitude: 0,
  longitude: 0,
};

const formatDistance = (distance: number) => {
  return distance < 1 ? `${(distance * 1000).toFixed(0)} m` : `${distance.toFixed(1)} km`;
};

const handleCall = (phone: string) => {
  const phoneNumber = phone || '';
  if (phoneNumber) {
    const formattedNumber = phoneNumber.replace(/\D/g, '');
    const url = Platform.select({
      ios: `tel:${formattedNumber}`,
      android: `tel:${formattedNumber}`,
    });
    Linking.openURL(url as string);
  }
};

const MapView = React.memo(
  (props: MapViewProps) => {
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
      const timer = setTimeout(() => setIsLoading(false), 500);
      return () => clearTimeout(timer);
    }, []);

    const webViewRef = useRef<WebView>(null);
    const { currentLocation, startForegroundTracking } = useLocationServices();

    const { theme } = useTheme();
    const { t } = useTranslation();

    const isFocused = useIsFocused();
    const [isMapReady, setIsMapReady] = useState(false);
    const [selectedDriver, setSelectedDriver] = useState<any>(null);

    const [state, setState] = useState({
      profileId: null as string | null,
      cancelReason: '',
      isModalVisible: false,
      freeRideRequestId: null as string | null,
      rating: 5,
    });

    const { loadNotificationState } = useNotifications();

    useEffect(() => {
      (async () => {
        try {
          const [profileId, freeRideRequestId] = await Promise.all([
            AsyncStorage.getItem('profileId'),
            AsyncStorage.getItem('freeRideRequestId'),
          ]);
          if (profileId) {
            await loadNotificationState();
          }
          setState(prevState => ({
            ...prevState,
            profileId,
            freeRideRequestId,
          }));
        } catch (error) {
          console.error('Error fetching IDs:', error);
        }
      })();
    }, []);

    const {
      driverLocations,
      loading: driversLoading,
      error: driversError,
      refetch: refetchDrivers,
    } = useDriverLocations(
      30000,
      [currentLocation?.coords?.latitude as number, currentLocation?.coords?.longitude as number],
      !!state.freeRideRequestId
    );

    const [updateDriver] = useMutation(UPDATE_DRIVER, {
      onCompleted: refetchDrivers,
      onError: error => {
        console.error('Error updating driver:', error);
      },
    });

    const { data: freeRideRequestsData, loading: freeRideRequestsLoading } = useSubscription(
      SUBSCRIBE_TO_ACTIVE_FREE_RIDE_REQUESTS,
      {
        skip: !state.profileId,
        variables: { profileId: state.profileId },
      }
    );

    const matchingFreeRideRequest = useMemo(() => {
      const currentRide = freeRideRequestsData?.free_rides?.[0];

      if (!currentRide) {
        AsyncStorage.removeItem('freeRideRequestId');
        return null;
      }

      if (currentRide?.status === 'accepted' || currentRide?.status === 'pending') {
        AsyncStorage.setItem('freeRideRequestId', currentRide.id);
        setState(prevState => ({
          ...prevState,
          freeRideRequestId: currentRide.id,
        }));
      }

      return currentRide?.driver?.driver_locations_current
        ? {
            // ...currentRide?.driver?.driver_locations_current,
            ...currentRide,
            status: currentRide?.status,
            vechicle_type: currentRide?.driver?.vehicles?.[0]?.vehicle_type,
          }
        : null;
    }, [freeRideRequestsData]);

    const userLocation = useMemo(
      () => ({
        latitude: currentLocation?.coords.latitude || props.latitude,
        longitude: currentLocation?.coords.longitude || props.longitude,
      }),
      [
        currentLocation?.coords.latitude,
        currentLocation?.coords.longitude,
        props.latitude,
        props.longitude,
      ]
    );

    const [updateFreeRideRequest, { loading: updateFreeRideRequestLoading }] = useMutation(
      UPDATE_FREE_RIDE_REQUEST,
      {
        onCompleted: () => {},
      }
    );

    useEffect(() => {
      if (isFocused && isMapReady) {
        refetchDrivers();
      }
    }, [isFocused, isMapReady, state.freeRideRequestId]);

    const coords = useMemo(
      () => ({
        latitude: currentLocation?.coords.latitude || props.latitude || INITIAL_LOCATION.latitude,
        longitude:
          currentLocation?.coords.longitude || props.longitude || INITIAL_LOCATION.longitude,
      }),
      [currentLocation, props.latitude, props.longitude]
    );

    useEffect(() => {
      if (!isMapReady || !webViewRef.current || !driverLocations) return;

      // webViewRef.current.injectJavaScript(`
      //   window.handleMessage(JSON.stringify({
      //     type: 'cabs',
      //     data: { cabs: ${JSON.stringify(!matchingFreeRideRequest? driverLocations || []: [])} }
      //   }));
      //   true;
      // `);

      if (matchingFreeRideRequest) {
        webViewRef.current.injectJavaScript(`
        window.handleMessage(JSON.stringify({
          type: 'currentRide',
          data:{currentLocation:  ${JSON.stringify(currentLocation)}  , currentRide: ${JSON.stringify(freeRideRequestsData?.free_rides?.[0] || null)}}
        }));
        true;
      `);
      } else {
        webViewRef.current.injectJavaScript(`
        window.handleMessage(JSON.stringify({
          type: 'currentRide',
          data: {currentLocation: ${JSON.stringify(null)}, currentRide: ${JSON.stringify(null)}}
        }));
        true;
      `);
      }
    }, [
      driverLocations,
      isMapReady,
      currentLocation,
      matchingFreeRideRequest?.driver?.driver_locations_current,
      webViewRef.current,
    ]);

    const handleCenterLocation = useCallback(() => {
      if (!isMapReady || !webViewRef.current || !currentLocation) return;

      const userLocation = {
        latitude: currentLocation?.coords.latitude || props.latitude,
        longitude: currentLocation?.coords.longitude || props.longitude,
      };

      webViewRef.current.injectJavaScript(`
      window.handleMessage(JSON.stringify({
        type: 'centerLocation',
        data: ${JSON.stringify(userLocation)}
      }));
      true;
    `);
    }, [currentLocation, isMapReady, props.latitude, props.longitude, webViewRef.current]);

    const handleNearbyCabs = useCallback(() => {
      router.push({
        pathname: '/nearby-cabs',
        params: {
          latitude: currentLocation?.coords.latitude || props.latitude,
          longitude: currentLocation?.coords.longitude || props.longitude,
        },
      });
    }, [currentLocation, props.latitude, props.longitude]);

    const handleDriverClose = useCallback(() => {
      setSelectedDriver(null);
    }, []);

    const handleMessage = useCallback(
      (event: any) => {
        try {
          const data = JSON.parse(event.nativeEvent.data);
          if (data.type === 'cabSelected') {
            const driverData = data.cab;
            if (props.onCabSelect) {
              props.onCabSelect(driverData);
            }
            setSelectedDriver(driverData);
          }
        } catch (e) {
          console.error('Error parsing message:', e);
        }
      },
      [props.onCabSelect]
    );

    const handleLoadEnd = useCallback(() => {
      setIsMapReady(true);
    }, []);

    useEffect(() => {
      const setupTracking = async () => {
        // if(!currentLocation){
        await startForegroundTracking();
        // }
      };
      if (isFocused) {
        setupTracking();
      }
    }, [isFocused, startForegroundTracking]);

    useEffect(() => {
      if (!isMapReady || !webViewRef.current || !currentLocation) {
        return;
      }

      const updateMapState = () => {
        webViewRef.current?.injectJavaScript(`
          try {
            window.handleMessage(JSON.stringify({
              type: 'location',
              data: ${JSON.stringify(userLocation)}
            }));

            window.handleMessage(JSON.stringify({
              type: 'cabs',
              data: { cabs: ${JSON.stringify(matchingFreeRideRequest ? [] : driverLocations || [])} }
            }));
          } catch(e) {
            console.error('Map update error:', e);
          }
          true;
        `);
      };

      // Add a small delay to ensure WebView is fully ready
      const timeoutId = setTimeout(updateMapState, 100);

      return () => clearTimeout(timeoutId);
    }, [isMapReady, currentLocation, driverLocations, userLocation, matchingFreeRideRequest]);

    const mapCoords = currentLocation?.coords || props.initialLocation;

    const handleCancelPress = useCallback(
      (freeRideId: string) => {
        setState(prevState => ({
          ...prevState,
          selectedFreeRideId: freeRideId,
          isModalVisible: true,
        }));
      },
      [setState]
    );

    const handleCancelConfirm = useCallback(async () => {
      try {
        const reason = state.cancelReason || 'User Cancelled';
        const { data } = await updateFreeRideRequest({
          variables: {
            id: freeRideRequestsData?.free_rides?.[0]?.id,
            data: {
              is_active: false,
              cancel_reason: reason,
              status: 'cancelled',
            },
          },
        });

        if (data?.update_free_rides_by_pk?.id) {
          // Update driver status
          await updateDriver({
            variables: {
              driverId: freeRideRequestsData?.free_rides?.[0]?.driver?.id,
              data: { is_busy: false },
            },
          });

          // Send notification
          // sendtoDriverNotification(
          //   data?.update_free_rides_by_pk?.driver?.notification_token,
          //   'Ride Cancelled',
          //   `Reason: ${reason}`
          // );

          // Clear storage
          await AsyncStorage.multiRemove(['freeRideRequestId']);

          // Reset all relevant state
          setState(prevState => ({
            ...prevState,
            isModalVisible: false,
            freeRideRequestId: null,
            cancelReason: '',
            selectedFreeRideId: null,
          }));

          // Force refresh map state
          if (webViewRef.current) {
            webViewRef.current.injectJavaScript(`
              window.handleMessage(JSON.stringify({
                type: 'mapUpdate',
                data: {
                  location: ${JSON.stringify(userLocation)},
                  cabs: ${JSON.stringify(driverLocations || [])}
                }
              }));
              true;
            `);
          }
        }
      } catch (error) {
        console.error('Error cancelling share:', error);
      }
    }, [
      freeRideRequestsData,
      setState,
      state.cancelReason,
      updateDriver,
      updateFreeRideRequest,
      userLocation,
      driverLocations,
    ]);

    if (isLoading || !currentLocation) {
      return <AnimatedLoadingScreen />;
    }

    return (
      <View style={{ flex: 1 }}>
        {!matchingFreeRideRequest && state?.freeRideRequestId && (
          <RatingModal freeRideRequestId={state?.freeRideRequestId} setState={setState} />
        )}

        <View style={styles.mapContainer}>
          {mapCoords && (
            <WebView
              ref={webViewRef}
              source={{
                html: htmlContent({
                  initialLocation: coords,
                  onCabSelect: props.onCabSelect,
                  latitude: mapCoords.latitude,
                  longitude: mapCoords.longitude,
                }),
              }}
              style={styles.map}
              scrollEnabled={false}
              bounces={false}
              showsHorizontalScrollIndicator={false}
              showsVerticalScrollIndicator={false}
              androidLayerType="hardware"
              renderToHardwareTextureAndroid
              javaScriptEnabled
              domStorageEnabled
              onLoadEnd={handleLoadEnd}
              onMessage={handleMessage}
              onError={syntheticEvent => {
                const { nativeEvent } = syntheticEvent;
                console.warn('WebView error: ', nativeEvent);
              }}
              onHttpError={syntheticEvent => {
                const { nativeEvent } = syntheticEvent;
                console.warn('WebView HTTP error: ', nativeEvent);
              }}
            />
          )}
          {selectedDriver && (
            <View
              style={{
                position: 'absolute',
                left: 6,
                right: 6,
                bottom: 6,
              }}
            >
              <NearbyVechicleCard
                item={selectedDriver}
                userLocation={userLocation}
                onClose={handleDriverClose}
              />
            </View>
          )}
          <FAB
            style={[styles.centerButton, { backgroundColor: theme.colors.surface }]}
            icon="crosshairs-gps"
            onPress={handleCenterLocation}
            color={theme.colors.primary}
            size="small"
          />
          {!driversLoading && !state?.freeRideRequestId && (
            <TouchableOpacity
              onPress={handleNearbyCabs}
              style={[styles.ridesNearby, { backgroundColor: theme.colors.surface }]}
            >
              <Text
                style={{
                  fontSize: 14,
                  paddingVertical: 8,
                  paddingHorizontal: 16,
                  fontWeight: 'bold',
                  color: driverLocations.length > 0 ? theme.colors.primary : theme.colors.error,
                }}
              >
                {driverLocations.length > 0
                  ? t('common.rides.available', {
                      count: driverLocations.length,
                      plural: t(`common.rides.${driverLocations.length === 1 ? 'ride' : 'rides'}`),
                    })
                  : t('common.rides.noRides')}
              </Text>
            </TouchableOpacity>
          )}
          {!isMapReady && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.colors.primary} />

              <Text style={{ color: theme.colors.onSurface }}>{t('mapView.loading')}</Text>
            </View>
          )}
        </View>

        {/* {matchingFreeRideRequest && !state?.freeRideRequestId ? (
          <NearbyVechicleCard
            item={matchingFreeRideRequest}
            userLocation={userLocation}
            handleCancelPress={handleCancelPress}
          />
        ) : (
          <FAB
            style={[styles.fab, { backgroundColor: theme.colors.primary }]}
            icon="car"
            label={t('mapView.findNearbyCabs')}
            onPress={handleNearbyCabs}
            color={theme.colors.onPrimary}
          />
        )} */}

        {!matchingFreeRideRequest && (
          <FAB
            style={[styles.fab, { backgroundColor: theme.colors.primary }]}
            icon="car"
            label={t('mapView.findNearbyCabs')}
            onPress={handleNearbyCabs}
            color={theme.colors.onPrimary}
            loading={isLoading || freeRideRequestsLoading || driversLoading}
            disabled={isLoading || freeRideRequestsLoading}
          />
        )}

        {matchingFreeRideRequest && state?.freeRideRequestId && (
          <NearbyVechicleCard
            item={matchingFreeRideRequest}
            userLocation={userLocation}
            handleCancelPress={handleCancelPress}
          />
        )}
        <Modal
          visible={state.isModalVisible}
          onDismiss={() => setState(prevState => ({ ...prevState, isModalVisible: false }))}
          style={styles.modalContainer}
        >
          <Surface style={styles.modalSurface}>
            <Title style={styles.modalTitle}>{t('mapView.selectCancellationReason')}</Title>
            <RadioButton.Group
              onValueChange={value =>
                setState(prevState => ({ ...prevState, cancelReason: value }))
              }
              value={state.cancelReason}
            >
              {[
                'Change of plans',
                'Found another ride',
                'Driver not responding',
                'Booked by mistake',
                'Long waiting time',
                'Driver is too far',
                'Other reason',
              ].map(reason => (
                <RadioButton.Item
                  key={reason}
                  label={reason}
                  value={reason}
                  labelStyle={styles.radioLabel}
                />
              ))}
            </RadioButton.Group>
            <TextInput
              mode="outlined"
              label={t('mapView.cancelReason')}
              value={state.cancelReason === 'Other' ? '' : state.cancelReason}
              onChangeText={text => setState(prevState => ({ ...prevState, cancelReason: text }))}
              multiline
              style={styles.input}
            />
            <Button
              mode="contained"
              onPress={handleCancelConfirm}
              buttonColor={theme.colors.error}
              style={styles.bookButton}
            >
              {t('mapView.confirm')}
            </Button>
            <Button
              mode="outlined"
              onPress={() => setState(prevState => ({ ...prevState, isModalVisible: false }))}
              style={styles.bookButton}
            >
              {t('mapView.cancel')}
            </Button>
          </Surface>
        </Modal>
      </View>
    );
  },
  (prevProps, nextProps) => {
    // Add custom comparison logic
    return prevProps.latitude === nextProps.latitude && prevProps.longitude === nextProps.longitude;
  }
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  mapContainer: {
    flex: 1,
    borderRadius: 12,
    marginTop: 8,
    marginBottom: 16,
    overflow: 'hidden', // Add this to ensure content stays within bounds
    backgroundColor: '#fff', // Add background color
  },
  map: {
    flex: 1,
    width: '100%',
    height: '100%',
    backgroundColor: 'transparent',
  },
  fab: {
    marginBottom: 16,
    // marginHorizontal: 16,
  },
  driverCard: {
    borderRadius: 12,
    position: 'absolute',
    bottom: 16,
    left: 8,
    right: 8,
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    alignSelf: 'center',
  },
  driverInfo: {
    flex: 1,
    flexDirection: 'row',
  },
  contentContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  mainInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  driverName: {
    fontSize: 16,
    fontWeight: '600',
  },
  closeButton: {},
  detailsContainer: {
    marginTop: 4,
    gap: 4,
  },
  fullScreenCardContent: {
    width: '90%',
    padding: 24,
    borderRadius: 16,
    maxWidth: 400,
  },
  fullScreenCardTitle: {
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 16,
  },
  ratingLabel: {
    textAlign: 'center',
    marginBottom: 16,
    fontSize: 16,
  },
  ratingContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 16,
    gap: 8,
  },
  starButton: {
    padding: 4,
  },
  ratingValue: {
    textAlign: 'center',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 24,
  },
  commentInput: {
    marginBottom: 24,
    fontSize: 16,
  },
  fullScreenCardButton: {
    marginTop: 8,
    padding: 4,
    borderRadius: 8,
  },
  fullScreenCardCancelButton: {
    marginTop: 12,
  },
  divider: {
    width: 1,
    height: 12,
    backgroundColor: '#e0e0e0',
    marginHorizontal: 4,
  },
  centerButton: {
    position: 'absolute',
    left: 16,
    top: 16,
    borderRadius: 28,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    zIndex: 1,
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 2,
  },
  ridesNearby: {
    position: 'absolute',
    right: 16,
    top: 16,
    borderRadius: 28,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    zIndex: 1,
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 2,
  },
  modalContainer: {
    flex: 1,
    margin: 16,
    borderRadius: 12,
  },
  modalSurface: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
  },
  modalTitle: {
    marginBottom: 16,
  },
  radioLabel: {
    fontSize: 14,
  },
  input: {
    backgroundColor: 'transparent',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  bookButton: {
    marginTop: 16,
  },
  driverItem: {
    borderRadius: 12,
    // overflow: 'hidden',
    margin: 16,
    padding: 16,
  },
  driverContent: {
    flexGrow: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  avatarContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textContainer: {
    marginLeft: 12,
    flex: 1,
  },
  // name: {
  //   fontSize: 16,
  //   fontWeight: '600',
  //   marginBottom: 4,
  // },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#e0e0e0',
    marginHorizontal: 8,
  },
  actionContainer: {
    alignItems: 'flex-end',
  },
  distanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  // distanceText: {
  //   marginLeft: 4,
  //   fontSize: 14,
  //   fontWeight: '500',
  // },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  requestRideButton: {
    flex: 1,
    marginTop: 6,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
  },
  fullScreenCard: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1000,
  },
  rideDetails: {
    marginBottom: 20,
  },
  rideDetailText: {
    fontSize: 19,
    marginBottom: 4,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  name: {
    fontSize: 15,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
    color: '#1A1A1A',
  },
  distanceBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    opacity: 0.7,
  },
  distanceText: {
    fontSize: 13,
    fontWeight: '500',
    color: '#666',
  },
  infoRow: {
    gap: 16,
  },
  vehicleInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statsInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoIcon: {
    marginRight: 4,
    opacity: 0.6,
  },
  infoText: {
    fontSize: 13,
    fontWeight: '400',
    color: '#666',
  },
  phoneButton: {
    margin: 0,
    marginLeft: 8,
  },
});

const htmlContent = (props: MapViewProps) => `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <style>
        * {
            margin: 0;
            padding: 0;
            -webkit-user-select: none;
            user-select: none;
        }
        html, body, #map {
            width: 100%;
            height: 100%;
            // overflow: hidden;
            background: #f8f9fa;
        }
        .bike-marker {
            width: 40px;
            height: 40px;
            will-change: transform;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .auto-marker{
          font-size: 14px;  
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 219, 0.5);
            padding: 6px;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
            width: 18px;
            height: 18px;
            transition: all 0.3s ease;
            border: 2px solid white;
        }
        .cab-marker {
            font-size: 18px;  
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 219, 0.5);
            padding: 6px;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
            width: 24px;
            height: 24px;
            transition: all 0.3s ease;
            border: 2px solid white;
        }
        .cab-marker:hover {
            transform: scale(1.5);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
        }
        .cab-tooltip-top {
            // background: rgba(52, 152, 219, 0.9) !important;
            border: none !important;
            border-radius: 8px !important;
            padding: 6px 10px !important;
            // color: white !important;
            font-size:  10px !important;
            font-weight: bold !important;
            letter-spacing: 0.3px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
            backdrop-filter: blur(4px) !important;
            max-width: 200px !important;
            text-align: center !important;
            line-height: 1.5 !important;
        }
        .cab-tooltip-top:before {
            // border-top-color: rgba(52, 152, 219, 0.9) !important;
        }
        // .cab-tooltip-bottom {
        //     background: rgba(52, 152, 219, 0.95) !important;
        //     border: none !important;
        //     border-radius: 8px !important;
        //     padding: 4px 8px !important;
        //     color: white !important;
        //     font-size: 11px !important;
        //     font-weight: bold !important;
        //     letter-spacing: 0.3px !important;
        //     box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        //     backdrop-filter: blur(4px) !important;
        //     max-width: 200px !important;
        //     text-align: center !important;
        // }
        // .cab-tooltip-bottom:before {
        //     border-bottom-color: rgba(52, 152, 219, 0.95) !important;
        // }
        .passenger-marker {
            font-size: 24px;  
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.6);
            padding: 0px;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(20, 152, 99, 0.3);
            width: 40px;
            height: 40px;
            border: 2px solid white;
            transition: all 0.3s ease;
        }
        .driver-icon {
            transform-origin: center;
            transition: transform 0.3s ease;
        }
        .leaflet-marker-icon {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
    </style>
</head>
<body>
    <div id="map"></div>
    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
    <script>
        let map;
        let markers = new Map();
        let currentRidePolyline;
        let driverRidePolyline;
        let pickupMarker;
        let driverMarker;
        let userMarker;
        let isMapInitialized = false;
        let lastUpdate = 0;
        const UPDATE_THRESHOLD = 16;

        const bikeIcon = L.divIcon({
            className: 'bike-marker',
            html: '<div class="passenger-marker"><div class="driver-icon">🙋‍♂️</div></div>',
            iconSize: [40, 40],
            iconAnchor: [20, 20]
        });

        function initializeMap(location) {
            if (!isMapInitialized) {
                map = L.map('map', {
                    zoomControl: false,
                    attributionControl: false,
                    fadeAnimation: true,
                    zoomAnimation: true,
                    markerZoomAnimation: false,
                    preferCanvas: true,
                    renderer: L.canvas({ tolerance: 5 })
                }).setView([location.latitude, location.longitude], 16);

                // Store the tile layer reference
                if (!map.tileLayer) {
                    // map.tileLayer = L.tileLayer('https://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}&scale=2', {
                    //     maxZoom: 22,
                    //     minZoom: 12,
                    //     subdomains: ['mt0', 'mt1', 'mt2', 'mt3'],
                    //     attribution: '&copy; <a href="https://www.google.com/maps">Google Maps</a>',
                    //     tileSize: 256,
                    //     updateWhenIdle: true,
                    //     keepBuffer: 2
                    // }).addTo(map);


                    // map.tileLayer = L.tileLayer('https://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}&scale=2', {
                    //       maxZoom: 20,
                    //       minZoom: 12,
                    //       subdomains: ['mt0', 'mt1', 'mt2', 'mt3'],
                    //       tileSize: 256,
                    //       updateWhenIdle: true,
                    //       keepBuffer: 2,
                    //       // Add these optimization parameters
                    //       detectRetina: true,
                    //       crossOrigin: true,
                    //       maxNativeZoom: 18,
                    //       zoomOffset: 0,
                    //   }).addTo(map);

                    map.tileLayer = L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                                    maxZoom: 22, // Reduced from 22 to prevent over-zooming
                                    minZoom: 9,
                                    tileSize: 256,
                                    updateWhenIdle: false, // Changed to false to update continuously
                                    keepBuffer: 1, // Reduced buffer size
                                    detectRetina: false, // Disable retina detection for better performance
                                    crossOrigin: true,
                                    maxNativeZoom: 18,
                                    zoomOffset: 0,
                                    subdomains: 'abcd',
                                    preferCanvas: true,
                                    // Add these performance optimizations
                                    updateWhenZooming: false, // Prevent updates during zoom
                                    noWrap: true, // Prevent tile wrapping around the world
                                    bounds: [
                                        [26.3478, 80.0982], // SW bounds of Nepal
                                        [30.4227, 88.2037]  // NE bounds of Nepal
                                    ],
                                    // Improved caching
                                    className: 'map-tiles',
                                    errorTileUrl: 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'
                                  }).addTo(map);
                                  
                                  
                                  
                }

              

                userMarker = L.marker([location.latitude, location.longitude], {
                    icon: bikeIcon,
                    zIndexOffset: 1000,
                }).addTo(map);

                isMapInitialized = true;
            }
        }

        function updateLocation(location) {
            const now = Date.now();
            if (now - lastUpdate < UPDATE_THRESHOLD) return;
            lastUpdate = now;

            if (!isMapInitialized) {
                initializeMap(location);
                return;
            }

            const pos = [location.latitude, location.longitude];
            userMarker.setLatLng(pos);
            
            if (location.heading !== undefined) {
                const markerElement = userMarker.getElement();
                if (markerElement) {
                    const iconDiv = markerElement.querySelector('.driver-icon');
                    if (iconDiv) {
                        const rotateStyle = 'rotate(' + location.heading.toString() + 'deg)';
                        iconDiv.style.transform = rotateStyle;
                    }
                }
            }
        }

        function updateCabs(data) {
            try {
                const { cabs } = JSON.parse(data);
              

                markers.forEach((marker, id) => {
                    if (!cabs.find(cab => cab?.id === id)) {
                        map.removeLayer(marker);
                        markers.delete(id);
                    }
                });

                cabs.forEach(cab => {
                    let marker = markers.get(cab?.id);
                    const latlng = [cab?.location?.coordinates[1], cab?.location?.coordinates[0]];
                   
                    
                    if (!marker) {
                        const markerHtml = '<div class="auto-marker"><div style="transform: rotate(' + (cab?.heading || 0) + 'deg);">' +  (cab?.driver?.vehicles?.[0]?.vehicle_type === 'auto' ? '🛺' : cab?.driver?.vehicles?.[0]?.vehicle_type === 'car'? '🚕' : cab?.driver?.vehicles?.[0]?.vehicle_type === 'motorcycle' ? '🏍️' : cab?.driver?.vehicles?.[0]?.vehicle_type==='scooter' ? '🛵' : '🛺') + '</div></div>';

                        marker = L.marker(latlng, {
                            icon: L.divIcon({
                                html: markerHtml,
                                className: '',
                                iconSize: [32, 32],
                                iconAnchor: [16, 16],
                              
                            })
                        });
                        
                        marker.addTo(map);

                        // const tooltipContent = 
                        //     '<div style="line-height: 1.4;">' +
                        //     '<div style="font-weight: 600;">' + (cab.driver.vehicles[0]?.license_plate || '') + '</div>' +
                        //     '</div>';

                        // marker.bindTooltip(tooltipContent, {
                        //     permanent: true,
                        //     direction: 'top',
                        //     className: 'cab-tooltip-top',
                        //     offset: [0, -10]
                        // });

                        marker.on('click', () => {
                            window.ReactNativeWebView.postMessage(JSON.stringify({
                                type: 'cabSelected',
                                cab: cab
                            }));
                        });

                        markers.set(cab?.id, marker);
                    } else {
                        marker.setLatLng(latlng);
                        const markerElement = marker.getElement();
                        if (markerElement) {
                            const cabIcon = markerElement.querySelector('.cab-marker div');
                            if (cabIcon) {
                                cabIcon.style.transform = 'rotate(' + (cab.heading || 0) + 'deg)';
                            }
                        }
                    }
                });
            } catch (e) {
                console.error('Error in updateCabs:', e);
            }
        }

        function centerLocation(location) {
          map.setView([location.latitude, location.longitude], 15);
        }


        function viewRide(data) {
          // If no current ride, return
          if (!data.currentRide) {
              map.removeLayer(currentRidePolyline);
              map.removeLayer(driverRidePolyline);
              currentRidePolyline = null;
              driverRidePolyline = null;

              map.removeLayer(driverMarker);
              map.removeLayer(pickupMarker);
              pickupMarker = null;
              driverMarker = null;
          
          return
          };

          // Get current location and ride pickup location
          const currentLocation = data.currentLocation?.coords || data.currentLocation;
          const ridePickup = data.currentRide.pickup_location?.coordinates;


        


          if (currentLocation && ridePickup) {
            // Remove existing polyline if any
            if (currentRidePolyline || driverRidePolyline) {
              map.removeLayer(currentRidePolyline);
              map.removeLayer(driverRidePolyline);
              currentRidePolyline = null;
              driverRidePolyline = null;
            };

            // Create polyline
            currentRidePolyline = L.polyline([
              [currentLocation.latitude, currentLocation.longitude],
              [ridePickup[1], ridePickup[0]]
            ], {    
              color: 'blue',
              weight: 3,
              opacity: 0.7,
              dashArray: '10, 10',  
              lineJoin: 'round',
              lineCap: 'round'
            }).addTo(map);

              driverRidePolyline = L.polyline([
              [data.currentRide.driver.driver_locations_current.location.coordinates[1], data.currentRide.driver.driver_locations_current.location.coordinates[0]],
              [ridePickup[1], ridePickup[0]]
            ], {    
              color: 'red',
              weight: 3,
              opacity: 0.7,
              dashArray: '10, 10',  
              lineJoin: 'round',
              lineCap: 'round'
            }).addTo(map);



              if(pickupMarker || driverMarker){
              map.removeLayer(driverMarker);
            map.removeLayer(pickupMarker);
            pickupMarker = null;
            driverMarker = null;
          }

          // Create pickup marker
         pickupMarker = L.marker([data.currentRide.pickup_location.coordinates[1], data.currentRide.pickup_location.coordinates[0]], {
            icon: L.divIcon({
              html: '<div style="font-size: 32px;">📍</div>',
              className: 'pickup-marker',
              iconSize: [32, 32],
              iconAnchor: [16, 16]
            })
          }).addTo(map);



            // Create pickup marker
         driverMarker = L.marker([data.currentRide.driver.driver_locations_current.location.coordinates[1], data.currentRide.driver.driver_locations_current.location.coordinates[0]], {
            icon: L.divIcon({
              html: '<div style="font-size: 24px;">' + (data.currentRide?.driver?.vehicles?.[0]?.vehicle_type === 'auto' ? '🛺' : data.currentRide?.driver?.vehicles?.[0]?.vehicle_type === 'car'? '🚕' : data.currentRide?.driver?.vehicles?.[0]?.vehicle_type === 'motorcycle' ? '🏍️' : data.currentRide?.driver?.vehicles?.[0]?.vehicle_type==='scooter' ? '🛵' : '🛺') + '</div>',
              className: 'pickup-marker',
              iconSize: [24, 24],
              iconAnchor: [12, 16]
            })
          }).addTo(map);


            // Optional: Fit bounds to show both points
            // map.fitBounds([
            //   [currentLocation.latitude, currentLocation.longitude],
            //   [ridePickup[1], ridePickup[0]]
            // ], { padding: [50, 50] });
          } 
        }


        window.handleMessage = function(data) {
            try {
                const message = JSON.parse(data);
                
                if (message.type === 'mapUpdate') {
                    const { location, cabs, currentRide } = message.data;
                    
                    // Update location if provided
                    if (location) {
                        updateLocation(location);
                    }
                    
                    // Update cabs if provided
                    if (cabs !== undefined) {
                        updateCabs(JSON.stringify({ cabs }));
                    }
                    
                    // Update current ride if provided
                    if (currentRide !== undefined) {
                        viewRide({
                            currentLocation: location,
                            currentRide
                        });
                    }
                } else if (message.type === 'centerLocation') {
                    // Keep the direct centerLocation handler for specific centering requests
                    centerLocation(message.data);
                } else if (message.type === 'location') {
                    updateLocation(message.data); 
                } else if (message.type === 'cabs') {
                    updateCabs(JSON.stringify(message.data));
                } else if (message.type === 'centerLocation') {
                  centerLocation(message.data);
                } else if (message.type === 'currentRide') {
                  viewRide(message.data);
                }
            } catch (e) {
                console.error('Error in handleMessage:', e);
            }
        };
    </script>
</body>
</html>
`;

export default MapView;

const RatingModal = ({
  freeRideRequestId,
  setState,
}: {
  freeRideRequestId: string;
  setState: React.SetStateAction<any>;
}) => {
  const [rating, setRating] = useState(5);
  const [comment, setComment] = useState('');
  const { theme } = useTheme();

  const { data, loading: ratingLoading } = useQuery(GET_FREE_RIDE_PK, {
    variables: { id: freeRideRequestId },
    skip: !freeRideRequestId,
  });

  const [createRating] = useMutation(CREATE_RATING, {
    onCompleted: () => {
      AsyncStorage.removeItem('freeRideRequestId');
      setState((prevState: any) => ({ ...prevState, freeRideRequestId: null }));
    },
  });

  const handleAcceptRating = useCallback(async () => {
    const { driver_id, profile_id } = data?.free_rides_by_pk;

    const { errors } = await createRating({
      variables: {
        object: {
          free_ride_id: freeRideRequestId,
          rating: rating,
          to_driver_id: driver_id,
          from_user_profile_id: profile_id,
          comment: comment.trim(),
        },
      },
    });

    if (errors) {
      console.error('Error creating rating:', errors);
    }
  }, [createRating, rating, comment, data]);
  const isCancelled = useMemo(() => data?.free_rides_by_pk?.status === 'cancelled', [data]);

  if (ratingLoading) {
    return null;
  }

  return (
    <View style={styles.fullScreenCard}>
      <Surface
        style={[styles.fullScreenCardContent, { backgroundColor: theme.colors.surface }]}
        elevation={4}
      >
        <Text
          variant="headlineMedium"
          style={[
            styles.fullScreenCardTitle,
            { color: isCancelled ? theme.colors.error : theme.colors.onSurface },
          ]}
        >
          {isCancelled ? 'Sorry, Your ride has been cancelled' : 'How was your ride?'}
        </Text>

        {isCancelled ? (
          <View style={styles.rideDetails}>
            <Text style={[styles.rideDetailText, { color: theme.colors.onSurface }]}>
              Reason: {data?.free_rides_by_pk?.cancel_reason || 'N/A'}
            </Text>
          </View>
        ) : (
          <>
            <Text style={[styles.ratingLabel, { color: theme.colors.onSurfaceVariant }]}>
              Rate your experience with the driver
            </Text>

            <View style={styles.ratingContainer}>
              {[1, 2, 3, 4, 5].map(star => (
                <TouchableOpacity
                  key={star}
                  onPress={() => setRating(star)}
                  style={styles.starButton}
                >
                  <MaterialCommunityIcons
                    name={rating >= star ? 'star' : 'star-outline'}
                    size={36}
                    color={rating >= star ? theme.colors.primary : theme.colors.onSurfaceVariant}
                  />
                </TouchableOpacity>
              ))}
            </View>

            <TextInput
              mode="outlined"
              label="Additional Feedback"
              value={comment}
              onChangeText={setComment}
              multiline
              numberOfLines={4}
              style={[styles.commentInput, { backgroundColor: theme.colors.background }]}
              placeholder="Share your experience (optional)"
            />

            <Button
              mode="contained"
              onPress={handleAcceptRating}
              style={[styles.fullScreenCardButton, { backgroundColor: theme.colors.primary }]}
              labelStyle={{ fontSize: 16 }}
            >
              Submit Rating
            </Button>
          </>
        )}

        <Button
          mode="outlined"
          onPress={() => {
            AsyncStorage.removeItem('freeRideRequestId');
            setState((prevState: any) => ({ ...prevState, freeRideRequestId: null }));
          }}
          style={styles.fullScreenCardCancelButton}
          textColor={theme.colors.primary}
        >
          {isCancelled ? 'Close' : 'Skip Rating'}
        </Button>
      </Surface>
    </View>
  );
};
