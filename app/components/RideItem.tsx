import React, { memo } from 'react';
import { View, StyleSheet, Pressable } from 'react-native';
import { Text, Surface, IconButton, Avatar, Chip, Divider } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { formatDistanceToNow, format } from 'date-fns';
import { MD3Theme } from 'react-native-paper/lib/typescript/types';
import { getStatusColor } from 'app/utils/status-colors';
import { COLORS } from '@/constants';

interface RideItemProps {
  ride: {
    id: string;
    status: string;
    created_at: string;
    pickup_address: string;
    dropoff_address?: string | null;
    cancel_reason?: string;
    distance?: number;
    duration?: number;
    fare?: number;
    payment_method?: string;
    user_profile: {
      full_name?: string;
      phone?: string;
      rating?: number;
      total_rides?: number;
    } | null;
    driver?: {
      name?: string;
      phone_number?: string;
      vehicle?: {
        vehicle_type?: string;
        license_plate?: string;
      };
    } | null;
  };
  theme: MD3Theme;
  t: (key: string, options?: any) => string;
  onCallPress: (phone: string) => void;
  onPress?: (rideId: string) => void;
}

const RideItem = memo(
  ({ ride, theme, t, onCallPress, onPress }: RideItemProps) => {
    const formattedTime = formatDistanceToNow(new Date(ride.created_at), {
      addSuffix: true,
    });
    const exactTime = format(new Date(ride.created_at), 'MMM d, h:mm a');
    const statusColor = getStatusColor(ride.status, theme);
    const hasPhoneNumber = ride.user_profile?.phone != null;

    // const renderRideMetrics = () => (
    //   <View style={styles.metricsContainer}>
    //     <View style={styles.metricItem}>
    //       <MaterialCommunityIcons
    //         name="map-marker-distance"
    //         size={16}
    //         color={theme.colors.primary}
    //       />
    //       <Text style={[styles.metricText, { color: theme.colors.onSurface }]}>100 km</Text>
    //     </View>

    //     <View style={styles.metricItem}>
    //       <MaterialCommunityIcons name="clock-outline" size={16} color={theme.colors.primary} />
    //       <Text style={[styles.metricText, { color: theme.colors.onSurface }]}>500 min</Text>
    //     </View>

    //     <View style={styles.metricItem}>
    //       <MaterialCommunityIcons name="currency-inr" size={16} color={theme.colors.primary} />
    //       <Text style={[styles.metricText, { color: theme.colors.onSurface }]}>
    //         {(400.987).toFixed(0)}
    //       </Text>
    //     </View>
    //   </View>
    // );

    return (
      <Pressable onPress={() => onPress?.(ride.id)}>
        <Surface style={[styles.rideCard, { backgroundColor: theme.colors.surface }]} elevation={2}>
          <View style={styles.rideHeader}>
            <View style={styles.statusContainer}>
              <Chip
                mode="flat"
                style={[styles.statusChip, { backgroundColor: statusColor + '15' }]}
                textStyle={{ color: statusColor, fontSize: 12, fontWeight: '600' }}
              >
                {t(`common.status.${ride.status.toLowerCase()}`)}
              </Chip>
            </View>

            <View style={styles.timeContainer}>
              <Text
                variant="labelSmall"
                style={{ fontSize: 12, color: theme.colors.onSurfaceVariant }}
              >
                {formattedTime}
              </Text>
              <Text
                variant="labelSmall"
                style={[styles.timeAgo, { color: theme.colors.onSurfaceVariant }]}
              >
                {exactTime}
              </Text>
            </View>
          </View>

          <View style={styles.locationContainer}>
            <View style={styles.locationItem}>
              <View style={styles.locationIconContainer}>
                <View style={[styles.locationDot, { backgroundColor: theme.colors.primary }]} />
                <View
                  style={[styles.locationLine, { backgroundColor: theme.colors.outlineVariant }]}
                />
              </View>
              <View style={styles.locationText}>
                <Text variant="labelSmall" style={{ color: theme.colors.onSurfaceVariant }}>
                  {t('common.pickup')}
                </Text>
                <Text
                  variant="bodySmall"
                  numberOfLines={2}
                  style={{ color: theme.colors.onSurface }}
                >
                  {ride.pickup_address || t('common.noData')}
                </Text>
              </View>
            </View>

            <View style={styles.locationItem}>
              <View style={styles.locationIconContainer}>
                <View style={[styles.locationDot, { backgroundColor: theme.colors.error }]} />
              </View>
              <View style={styles.locationText}>
                <Text variant="labelSmall" style={{ color: theme.colors.onSurfaceVariant }}>
                  {t('common.dropoff')}
                </Text>
                <Text
                  variant="bodySmall"
                  numberOfLines={2}
                  style={{ color: theme.colors.onSurface }}
                >
                  {ride.dropoff_address || t('common.noData')}
                </Text>
              </View>
            </View>
          </View>

          <Divider style={[styles.divider, { backgroundColor: theme.colors.outlineVariant }]} />

          <View style={styles.userInfo}>
            <View>
              <View style={styles.userProfile}>
                {/* <Avatar.Icon
                  icon="account"
                  size={36}
                  style={{ backgroundColor: theme.colors.onPrimaryContainer }}
                  color={theme.colors.primary}
                /> */}

                <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                  {' '}
                  {ride?.driver?.vehicle?.vehicle_type === 'auto'
                    ? '🛺'
                    : ride?.driver?.vehicle?.vehicle_type === 'car'
                      ? '🚕'
                      : ride?.driver?.vehicle?.vehicle_type === 'motorcycle'
                        ? '🏍️'
                        : ride?.driver?.vehicle?.vehicle_type === 'scooter'
                          ? '🛵'
                          : '🛺'}{' '}
                </Text>
                <View style={styles.userDetails}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                    {ride?.driver?.name || t('common.noData')}
                  </Text>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                    {ride?.driver?.vehicle?.license_plate || t('common.noData')}
                  </Text>
                </View>
              </View>
            </View>
            {hasPhoneNumber && (
              <IconButton
                icon="phone"
                size={20}
                style={styles.phoneButton}
                onPress={() => onCallPress(ride?.driver?.phone_number || '')}
                iconColor={theme.colors.primary}
              />
            )}
          </View>

          {ride.status?.toLowerCase() === 'cancelled' && ride.cancel_reason && (
            <View style={styles.cancelReason}>
              <MaterialCommunityIcons name="alert-circle" size={16} color={theme.colors.error} />
              <Text variant="bodySmall" style={{ color: theme.colors.error }}>
                {t('settings.rideHistory.cancelReason', { reason: ride.cancel_reason })}
              </Text>
            </View>
          )}
        </Surface>
      </Pressable>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.ride.id === nextProps.ride.id &&
      prevProps.ride.status === nextProps.ride.status &&
      prevProps.theme === nextProps.theme
    );
  }
);

const styles = StyleSheet.create({
  rideCard: {
    margin: 8,
    padding: 16,
    borderRadius: 16,
  },
  rideHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  timeContainer: {
    alignItems: 'flex-end',
  },
  statusChip: {
    height: 28,
  },
  timeAgo: {
    fontSize: 12,
    opacity: 0.7,
  },
  phoneButton: {
    marginLeft: 0,
  },
  locationContainer: {
    gap: 12,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  locationIconContainer: {
    width: 20,
    alignItems: 'center',
  },
  locationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  locationLine: {
    width: 2,
    height: 24,
    marginTop: 4,
  },
  locationText: {
    flex: 1,
    gap: 2,
  },
  metricsContainer: {
    flexDirection: 'row',
    gap: 16,
    marginTop: 12,
    paddingVertical: 8,
    paddingHorizontal: 32,
  },
  metricItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metricText: {
    fontSize: 14,
    fontWeight: '500',
  },
  divider: {
    marginVertical: 12,
  },
  userInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userProfile: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 12,
  },
  userDetails: {
    gap: 2,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 12,
    fontWeight: '500',
  },
  ridesCount: {
    fontSize: 12,
  },
  paymentChip: {
    backgroundColor: COLORS.error,
    height: 28,
  },
  cancelReason: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    padding: 8,
    borderRadius: 8,
  },
});

export default RideItem;
