import React, { useMemo, useCallback, useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/theme/ThemeContext';
import { useMutation } from '@apollo/client';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import { useNotifications } from 'app/contexts/NotificationContext';
import { CREATE_FREE_RIDE_REQUEST } from 'app/graphql/nearby';
import { getPickupAddress, formatDistance, calculateDistance } from 'app/utils/location';
import { router } from 'expo-router';
import { Alert, View, StyleSheet, TouchableOpacity, Linking, Platform, Image } from 'react-native';
import { Button, Surface, Text, Modal, Portal } from 'react-native-paper';
import { sendtoDriverNotification } from 'app/utils/notification-sender';
// import DriverStatus from './DriverStatus';

const handleCall = (phone: string) => {
  const phoneNumber = phone || '';
  if (phoneNumber) {
    const formattedNumber = phoneNumber.replace(/\D/g, '');
    const url = Platform.select({
      ios: `tel:${formattedNumber}`,
      android: `tel:${formattedNumber}`,
    });
    Linking.openURL(url as string);
  }
};

// const sendtoDriverNotification = async (driverPushToken: string, title: string, body: string) => {
//   if (!driverPushToken) return;

//   try {
//     const response = await axios.post(
//       'https://exp.host/--/api/v2/push/send',
//       {
//         to: driverPushToken,
//         title,
//         body,
//         sound: 'notification_sound.wav',
//         priority: Notifications.AndroidNotificationPriority.HIGH,
//         trigger: {
//           channelId: 'default',
//         },
//       },
//       {
//         headers: {
//           'Content-Type': 'application/json',
//         },
//       }
//     );
//   } catch (error) {
//     console.error('Error sending notification:', error);
//   }
// };

const VEHICLE_TYPES = [
  {
    id: 'auto',
    label: 'Auto-Rickshaw',
    icon: require('../../assets/images/auto-rickshaw.png'),
  },
  {
    id: 'toto',
    label: 'E-Rickshaw',
    icon: require('../../assets/images/toto.png'),
  },
  {
    id: 'motorcycle',
    label: 'Bike',
    icon: require('../../assets/images/bike.png'),
  },
  {
    id: 'scooter',
    label: 'Scooter',
    icon: require('../../assets/images/scooter.png'),
  },
  {
    id: 'car',
    label: 'Taxi',
    icon: require('../../assets/images/car.png'),
  },
];

const NearbyVechicleCard = ({
  item,
  userLocation,
  onClose,
  handleCancelPress,
}: {
  item: any;
  userLocation: {
    latitude: number;
    longitude: number;
  };
  onClose?: () => void;
  handleCancelPress?: (freeRideId: string) => void;
}) => {
  const { theme } = useTheme();

  const { latitude, longitude } = userLocation;

  const rating = item?.driver?.ratings_aggregate?.aggregate?.avg?.rating || 0;
  const ratingDisplay = rating > 0 ? rating.toFixed(1) : 'New';

  // const { pushToken } = useNotifications();

  const { t } = useTranslation();

  // const [updateUserProfile, { loading: updateUserProfileLoading }] =
  //   useMutation(UPDATE_USER_PROFILE);

  const [createFreeRideRequest, { loading: createFreeRideRequestLoading }] = useMutation(
    CREATE_FREE_RIDE_REQUEST,
    {
      onCompleted: () => {
        !onClose && router.back();
      },
    }
  );

  const distance = useMemo(
    () =>
      calculateDistance(
        latitude,
        longitude,
        item?.location?.coordinates?.[1],
        item?.location?.coordinates?.[0]
      ),
    [item?.location?.coordinates, latitude, longitude]
  );

  const handleRequestRide = useCallback(
    async (driver_locations_current: any) => {
      try {
        onClose && onClose();

        const pickupAddress = await getPickupAddress(Number(latitude), Number(longitude));

        const { data, errors } = await createFreeRideRequest({
          variables: {
            input: {
              driver_id: driver_locations_current?.driver?.id,
              profile_id: await AsyncStorage.getItem('profileId'),
              pickup_location: {
                type: 'Point',
                coordinates: [Number(longitude), Number(latitude)],
              },
              pickup_address: pickupAddress || null,
            },
          },
        });

        if (data?.insert_free_rides_one?.id) {
          sendtoDriverNotification(
            data?.insert_free_rides_one?.driver?.notification_token,
            'You have a new Ride request',
            'Respond quickly!'
          );
        }

        // const { data, errors } = await updateUserProfile({
        //   variables: {
        //     id: await AsyncStorage.getItem('profileId'),
        //     data: {
        //       location: {
        //         type: 'Point',
        //         coordinates: [Number(longitude), Number(latitude)],
        //       },
        //     },
        //   },
        // });
      } catch (error) {
        console.error('Ride request error:', error);
        Alert.alert('Error', 'Could not request ride. Please try again.');
      }
    },
    [latitude, longitude, createFreeRideRequest, onClose]
  );

  const getStatusInfo = useCallback(
    (status: string) => {
      switch (status) {
        case 'pending':
          return {
            message: t('ride.status.pendingMessage'),
            color: theme.colors.tertiary || '#FFA500',
          };
        case 'accepted':
          return {
            message: t('ride.status.acceptedMessage'),
            color: theme.colors.primary || '#007BFF',
          };
        case 'arrived':
          return {
            message: t('ride.status.arrivedMessage'),
            color: theme.colors.surface || '#28A745',
          };
        default:
          return {
            message: t('ride.status.unknownMessage'),
            color: theme.colors.error || '#6C757D',
          };
      }
    },
    [theme, t]
  );

  const statusInfo = getStatusInfo(item.status);

  const [timeLeft, setTimeLeft] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const CANCEL_COOLDOWN = 30; // 30 seconds cancellation cooldown time

  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleConfirmRequest = () => {
    setShowConfirmation(false);
    handleRequestRide(item);
  };

  useEffect(() => {
    // Calculate time left for cancellation cooldown
    if (item?.created_at) {
      const createdAt = new Date(item.created_at).getTime();
      const now = new Date().getTime();
      const elapsedSeconds = Math.floor((now - createdAt) / 1000);

      if (elapsedSeconds < CANCEL_COOLDOWN) {
        setTimeLeft(CANCEL_COOLDOWN - elapsedSeconds);

        // Start the countdown timer
        timerRef.current = setInterval(() => {
          setTimeLeft(prev => {
            if (prev <= 1) {
              if (timerRef.current) {
                clearInterval(timerRef.current);
              }
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      }
    }

    return () => {
      // Clear the timer if it exists
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [item?.created_at]);

  const formatTime = (seconds: number) => {
    return `${String(Math.floor(seconds / 60)).padStart(2, '0')}:${String(seconds % 60).padStart(2, '0')}`;
  };

  return (
    <>
      <Surface
        style={[
          styles.driverItem,
          {
            backgroundColor: theme.colors.surface,
          },
        ]}
      >
        <View style={styles.driverContent}>
          <View style={styles.mainInfo}>
            <View
              style={[styles.avatarContainer, { backgroundColor: theme.colors.surfaceVariant }]}
            >
              <Image
                source={
                  VEHICLE_TYPES.find(type => type?.id === item?.driver?.vehicles?.[0]?.vehicle_type)
                    ?.icon
                }
                style={styles.vehicleTypeIcon}
                resizeMode="contain"
              />
            </View>

            <View style={styles.textContainer}>
              <View style={styles.nameRow}>
                <Text style={[styles.name, { color: theme.colors.onSurface }]}>
                  {item?.driver?.name || 'Unknown Driver'}
                </Text>
                <Text style={[styles.localUnit, { color: theme.colors.onSurfaceVariant }]}>
                  {item?.driver?.local_unit}
                </Text>
              </View>

              {/* <DriverStatus updatedAt={item?.updated_at} /> */}

              <View style={styles.infoRow}>
                <Text style={[styles.infoText, { color: theme.colors.onSurfaceVariant }]}>
                  {item?.driver?.vehicles?.[0]?.make || 'No Make'}{' '}
                  {item?.driver?.vehicles?.[0]?.model || 'No Model'}
                </Text>
                <Text style={[styles.ratingText, { color: theme.colors.onSurfaceVariant }]}>
                  ⭐
                  {item?.driver?.ratings_aggregate?.aggregate?.avg?.rating !== null &&
                    item?.driver?.ratings_aggregate?.aggregate?.avg?.rating.toFixed(1)}{' '}
                  {''}(
                  {item?.driver?.completedRides?.aggregate.count >= 2
                    ? item?.driver?.completedRides?.aggregate.count
                    : 'New'}
                  )
                </Text>
              </View>

              <View style={styles.statsRow}>
                <Text style={[styles.infoText, { color: theme.colors.onSurfaceVariant }]}>
                  {item?.driver?.vehicles?.[0]?.license_plate || 'No Plate'}
                  {/* •{' '}
                  {item?.driver?.vehicles?.[0]?.color || 'No Color'} */}
                </Text>
                {!handleCancelPress && (
                  <View style={styles.distanceContainer}>
                    <MaterialCommunityIcons
                      name="map-marker-distance"
                      size={12}
                      color={theme.colors.onSurfaceVariant}
                    />

                    <Text style={[styles.distanceText, { color: theme.colors.onSurfaceVariant }]}>
                      {formatDistance(distance || 0)}
                    </Text>
                  </View>
                )}
              </View>
            </View>
          </View>

          {handleCancelPress ? (
            <>
              <View
                style={{
                  backgroundColor: statusInfo.color + '10',
                  paddingVertical: 4,
                  paddingHorizontal: 12,
                  borderRadius: 50,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginTop: 12,
                }}
              >
                <Text style={{ color: statusInfo.color, fontSize: 16, fontWeight: 'bold' }}>
                  {statusInfo.message}
                </Text>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginTop: 6,
                }}
              >
                <Button
                  mode="text"
                  onPress={() => handleCancelPress && handleCancelPress(item?.free_ride_id)}
                  disabled={timeLeft > 0}
                  textColor={timeLeft > 0 ? theme.colors.outline : theme.colors.error}
                  icon={'cancel'}
                >
                  {timeLeft > 0 ? `Cancel (${formatTime(timeLeft)})` : t('common.cancel')}
                </Button>
                <Button
                  mode="elevated"
                  onPress={() => handleCall(item?.driver?.phone_number)}
                  // buttonColor={theme.colors.secondary}
                  // textColor={theme.colors.primary}
                  icon={'phone'}
                >
                  {t('common.callDriver')}
                </Button>
              </View>
            </>
          ) : (
            <View>
              {distance > 15 ? (
                <Text
                  style={{
                    color: theme.colors.error,
                    fontSize: 12,
                    marginTop: 16,
                    textAlign: 'center',
                    fontWeight: '500',
                  }}
                >
                  {t('nearbyCabs.distanceError', {
                    defaultValue: "Can't book, driver is far away from your location",
                  })}
                </Text>
              ) : (
                <Button
                  mode="contained"
                  onPress={() => setShowConfirmation(true)}
                  loading={createFreeRideRequestLoading}
                  disabled={createFreeRideRequestLoading}
                  style={[{ marginTop: 16, borderRadius: 16 }]}
                >
                  {t('nearbyCabs.book')}
                </Button>
              )}
            </View>
          )}
        </View>
        {onClose && (
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={32} color={theme.colors.onSurface} />
          </TouchableOpacity>
        )}
      </Surface>

      <Portal>
        <Modal
          visible={showConfirmation}
          onDismiss={() => setShowConfirmation(false)}
          contentContainerStyle={[styles.modalContainer, { backgroundColor: theme.colors.surface }]}
        >
          <View style={styles.modalContent}>
            <Text style={[styles.modalTitle, { color: theme.colors.onSurface }]}>
              {t('nearbyCabs.confirmBooking')}
            </Text>

            <Text
              style={[styles.modalMessage, { color: theme.colors.onSurfaceVariant, fontSize: 16 }]}
            >
              {t('nearbyCabs.confirmBookingMessage')}
            </Text>

            <View style={styles.driverPreview}>
              <Image
                source={
                  VEHICLE_TYPES.find(type => type?.id === item?.driver?.vehicles?.[0]?.vehicle_type)
                    ?.icon
                }
                style={styles.previewIcon}
                resizeMode="contain"
              />
              <View>
                <Text style={{ color: theme.colors.onSurface, fontSize: 16 }} numberOfLines={1}>
                  {item?.driver?.name}
                </Text>
                <Text
                  style={{ color: theme.colors.onSurfaceVariant, fontSize: 14 }}
                  numberOfLines={1}
                >
                  {item?.driver?.vehicles?.[0]?.make} {item?.driver?.vehicles?.[0]?.model}
                </Text>
                <Text
                  style={{ color: theme.colors.onSurfaceVariant, fontSize: 14 }}
                  numberOfLines={1}
                >
                  {item?.driver?.vehicles?.[0]?.license_plate}
                </Text>
              </View>
            </View>

            <View style={styles.modalActions}>
              <Button
                mode="outlined"
                onPress={() => setShowConfirmation(false)}
                style={{ flex: 1 }}
              >
                {t('common.cancel')}
              </Button>
              <Button
                mode="contained"
                onPress={handleConfirmRequest}
                style={{ flex: 1 }}
                loading={createFreeRideRequestLoading}
              >
                {t('common.confirm')}
              </Button>
            </View>
          </View>
        </Modal>
      </Portal>
    </>
  );
};

export default NearbyVechicleCard;

const styles = StyleSheet.create({
  driverItem: {
    borderRadius: 12,
    padding: 12,
    marginBottom: 8,
  },
  driverContent: {
    gap: 2,
  },
  mainInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    padding: 6,
    borderRadius: 10,
    height: 60,
    width: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  vehicleTypeIcon: {
    width: 60,
    height: 60,
  },
  textContainer: {
    flex: 1,
    marginLeft: 10,
    gap: 4,
  },
  nameRow: {
    // flexDirection: 'row',
    // alignItems: 'center',
    // gap: 6,
  },
  name: {
    fontSize: 15,
    fontWeight: '600',
  },
  localUnit: {
    fontSize: 11,
    opacity: 0.7,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 2,
  },
  infoText: {
    fontSize: 12,
  },
  statsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 2,
  },
  ratingText: {
    fontSize: 12,
  },
  distanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  distanceText: {
    fontSize: 12,
  },
  unitText: {
    fontSize: 10,
    opacity: 0.7,
  },
  statusBadge: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 50,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  bookButton: {
    marginTop: 16,
    borderRadius: 12,
    height: 44,
  },
  errorText: {
    fontSize: 14,
    marginTop: 16,
    textAlign: 'center',
    fontWeight: '500',
  },
  closeButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  distanceBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  modalContainer: {
    margin: 20,
    borderRadius: 16,
    padding: 20,
  },
  modalContent: {
    alignItems: 'center',
    gap: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 8,
  },
  driverPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    padding: 12,
    borderRadius: 12,
    backgroundColor: '#00000010',
    width: '100%',
  },
  previewIcon: {
    width: 40,
    height: 40,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
    marginTop: 8,
  },
});
