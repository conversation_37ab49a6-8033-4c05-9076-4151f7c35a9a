import { useIsFocused } from '@react-navigation/native';
import React from 'react';
import { StatusBar, StyleSheet } from 'react-native';
import { SafeAreaView, SafeAreaViewProps } from 'react-native-safe-area-context';
import { useTheme } from '../../src/theme/ThemeContext';

export enum StatusBarType {
  PRIMARY_LIGHT = 'primaryLight',
  BACKGROUND_DARK = 'backgroundDark',
  WHITE_DARK = 'whiteDark',
}

interface Props extends SafeAreaViewProps, React.ComponentProps<typeof SafeAreaView> {
  children: React.ReactNode;
  statusBarType?: StatusBarType;
}

function ScreenWrapper({
  children,
  statusBarType = StatusBarType.PRIMARY_LIGHT,
  style,
  ...props
}: Props) {
  const isFocused = useIsFocused();
  const { isDarkMode, theme } = useTheme();

  const getStatusBarContent = (statusBarType: StatusBarType) => {
    switch (statusBarType) {
      case StatusBarType.PRIMARY_LIGHT:
        return (
          <StatusBar
            translucent={false}
            backgroundColor={theme.colors.background}
            hidden={false}
            barStyle="dark-content"
          />
        );
      case StatusBarType.BACKGROUND_DARK:
        return (
          <StatusBar
            translucent={false}
            backgroundColor={theme.colors.background}
            hidden={false}
            barStyle="light-content"
          />
        );
      case StatusBarType.WHITE_DARK:
        return (
          <StatusBar
            translucent={false}
            backgroundColor={theme.colors.primary}
            hidden={false}
            barStyle={theme.dark ? 'light-content' : 'dark-content'}
            animated={true}
          />
        );
      default:
        return null;
    }
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: theme.colors.background }, style]}
      {...props}
    >
      {isFocused &&
        getStatusBarContent(
          isDarkMode ? StatusBarType.BACKGROUND_DARK : StatusBarType.PRIMARY_LIGHT
        )}
      {children}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default ScreenWrapper;
