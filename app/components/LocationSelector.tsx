import React, { useCallback } from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { Surface, Text, IconButton, Divider } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useLocation } from '../contexts/LocationContext';
import { useDriverLocations } from 'app/hooks/useDriverLocations';
import useLocationServices from 'app/hooks/useLocationServices';
import { calculateFare } from '../utils/fareCalculator';
import { useTheme } from '@/theme/ThemeContext';
import { useRoute } from 'app/contexts/RouteContext';

export default function LocationSelector() {
  const { dropoffLocation, setDropoffLocation } = useLocation();
  const { currentLocation } = useLocationServices();
  const { theme } = useTheme();
  const { distance } = useRoute();

  // Get driver locations using current location coordinates
  const { driverLocations } = useDriverLocations(
    30000, // poll interval
    [
      currentLocation?.coords?.latitude || 0,
      currentLocation?.coords?.longitude || 0
    ],
    !currentLocation?.coords // skip if no current location
  );


  const handleDropoffPress = () => {
    router.push('/dropoff-location');
  };

  const handleNearbyCabs = useCallback(() => {
    router.push({
      pathname: '/nearby-cabs',
      params: {
        latitude: currentLocation?.coords.latitude || 0,
        longitude: currentLocation?.coords.longitude || 0
      }
    });
  }, [currentLocation]);


  const handleClearLocation = () => {
    setDropoffLocation(null);
  };



  return (
    <Surface style={[styles.container, { backgroundColor: theme.colors.surface }]} elevation={4}>
      <View style={styles.handle} />

      <View style={styles.header}>
        <Text variant="titleMedium" style={[styles.title, { color: theme.colors.onSurface }]}>
          Where to?
        </Text>
        {dropoffLocation && (
          <IconButton
            icon="close"
            size={20}
            iconColor={theme.colors.onSurfaceVariant}
            onPress={handleClearLocation}
          />
        )}
      </View>

      <TouchableOpacity
        style={styles.locationButton}
        onPress={handleDropoffPress}
      >
        <View style={styles.locationContent}>
          <View style={styles.locationIconContainer}>
            <MaterialCommunityIcons
              name="map-marker"
              size={20}
              color={theme.colors.primary}
            />
          </View>
          <View style={styles.locationTextContainer}>
            <Text variant="bodyLarge" numberOfLines={2} style={{ color: theme.colors.onSurface }}>
              {dropoffLocation?.description || 'Enter destination'}
            </Text>
            {!dropoffLocation && (
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Choose your destination on the map
              </Text>
            )}

          </View>

          <IconButton
            icon="chevron-right"
            size={20}
            iconColor={theme.colors.onSurfaceVariant}
          />
        </View>
      </TouchableOpacity>
      {dropoffLocation && distance && (
        <View style={styles.tripInfoContainer}>
          <View style={styles.tripInfoItem}>
            <MaterialCommunityIcons
              name="map-marker-distance"
              size={16}
              color={theme.colors.primary}
            />
            <View style={styles.tripInfoText}>
              <Text variant="labelSmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Distance
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                {`${(distance / 1000).toFixed(1)} km`}
              </Text>
            </View>
          </View>

          <View style={styles.tripInfoItem}>
            <MaterialCommunityIcons
              name="clock-outline"
              size={16}
              color={theme.colors.primary}
            />
            <View style={styles.tripInfoText}>
              <Text variant="labelSmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Duration
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                {`${Math.ceil((distance / 1000) / 40 * 60)} min`}
              </Text>
            </View>
          </View>

          <View style={styles.tripInfoItem}>
            <MaterialCommunityIcons
              name="cash"
              size={16}
              color={theme.colors.primary}
            />
            <View style={styles.tripInfoText}>
              <Text variant="labelSmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Estd Fare
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                {`Rs. ${calculateFare(distance / 1000)}`}
              </Text>
            </View>
          </View>
        </View>
      )}

      {dropoffLocation && (
        <>
          <Divider style={styles.divider} />
          <TouchableOpacity
            style={[
              styles.nearbyButton,
              { backgroundColor: theme.colors.primary }
            ]}
            onPress={handleNearbyCabs}
          >
            <MaterialCommunityIcons
              name="car"
              size={24}
              color={theme.colors.onPrimary}
            />
            <Text style={[styles.buttonText, { color: theme.colors.onPrimary }]}>
              View Nearby Drivers ({driverLocations.length})
            </Text>
          </TouchableOpacity>
        </>
      )}
    </Surface>
  );
}

const styles = StyleSheet.create({
  container: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 16,
    width: '100%',
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  handle: {
    width: 40,
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    alignSelf: 'center',
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 8,
    marginBottom: 12,
  },
  title: {
    fontWeight: '700',
    fontSize: 20,
    letterSpacing: 0.15,
  },
  locationButton: {
    marginBottom: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    borderRadius: 12,
    padding: 4,
  },
  locationContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  locationIconContainer: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.04)',
    borderRadius: 18,
  },
  locationTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  divider: {
    marginVertical: 12,
    height: 1,
    opacity: 0.08,
  },
  nearbyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 12,
    marginHorizontal: 4,
    marginBottom: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  tripInfoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
    paddingVertical: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    borderRadius: 12,
    marginBottom: 12,
  },
  tripInfoItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 8,
  },
  tripInfoText: {
    flex: 1,
  },
  distanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginLeft: 16
  }
});
