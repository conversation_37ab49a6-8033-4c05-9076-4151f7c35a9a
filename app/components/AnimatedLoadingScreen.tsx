import { useTheme } from '@/theme/ThemeContext';
import { useRef, useEffect } from 'react';
import { Animated, Easing, StyleSheet, View } from 'react-native';

const AnimatedLoadingScreen = () => {
  const { theme } = useTheme();
  // const spinValue = useRef(new Animated.Value(0)).current;
  const moveValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(moveValue, {
          toValue: 1,
          duration: 1500,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(moveValue, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  // const spin = spinValue.interpolate({
  //   inputRange: [0, 1],
  //   outputRange: ['0deg', '360deg'],
  // });

  const move = moveValue.interpolate({
    inputRange: [0, 1],
    outputRange: [-200, 300],
  });

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Animated.Image
        source={require('../../assets/images/bolaoo-logo.png')}
        style={[styles.icon, { transform: [{ translateX: move }] }]}
        resizeMode="contain"
      />
    </View>
  );
};

export default AnimatedLoadingScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    // fontSize: 48,
    width: 120,
    height: 100,
  },
});
