import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from 'react-native-paper';

interface DriverStatusProps {
  updatedAt?: string;
}

 const getDriverStatus = (colors: any, updatedAt?: string) => {
  if (!updatedAt) return { text: 'Unavailable', color: colors.error };
  
  const lastUpdate = new Date(updatedAt);
  const now = new Date();
  const diffMinutes = (now.getTime() - lastUpdate.getTime()) / (1000 * 60);
  
  if (diffMinutes <= 5) return { text: 'Active', color: colors.primary };
  if (diffMinutes <= 10) return { text: 'Away', color: '#FFA500' };
  return { text: 'Away', color: colors.error };
};

export const formatLastSeen = (date: string) => {
  const lastUpdate = new Date(date);
  const now = new Date();
  const diffMs = now.getTime() - lastUpdate.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  
  if (diffSeconds < 60) {
    return null;
  }
  if (diffMinutes < 60) {
    return `${diffMinutes} ${diffMinutes === 1 ? 'minute' : 'minutes'} ago`;
  }
  
  const diffHours = Math.floor(diffMinutes / 60);
  if (diffHours === 1) return '1 hour ago';
  if (diffHours < 24) return `${diffHours} hours ago`;
  
  const diffDays = Math.floor(diffHours / 24);
  if (diffDays === 1) return '1 day ago';
  return `${diffDays} days ago`;
};

export const DriverStatus = ({ updatedAt }: DriverStatusProps) => {
  const theme = useTheme();
  const status = getDriverStatus(theme.colors, updatedAt);
  const lastSeen = updatedAt ? formatLastSeen(updatedAt) : null;

  return (
    <View style={styles.container}>
      <MaterialCommunityIcons 
        name={status.text === 'Active' ? 'circle' : 'circle-outline'} 
        size={16} 
        color={status.color} 
      />
      <Text style={[styles.text, { color: status.color }]}>
        {status.text}
      </Text>
      {updatedAt && lastSeen && (
        <>
          <View style={styles.dot} />
          <Text style={[styles.text, { color: theme.colors.onSurfaceVariant }]}>
            {lastSeen}
          </Text>
        </>
      )}
    </View>
  );
};

export default DriverStatus;


const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  text: {
    fontSize: 12,
  },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    marginHorizontal: 4,
  },
});
