import { useQuery, useMutation } from '@apollo/client';
import { useUserId } from '@nhost/react';
import { GET_DRIVER, CREATE_DRIVER, CHECK_EXISTING_DRIVER } from '../graphql/driver';
import { router } from 'expo-router';

export function useDriver() {
  const userId = useUserId();

  const { data: driverData, loading: driverLoading, error: driverError, refetch } = useQuery(
    GET_DRIVER,
    {
      variables: { userId },
      skip: !userId,
      fetchPolicy: 'network-only',
    }
  );

  const { data: existingDriverData } = useQuery(
    CHECK_EXISTING_DRIVER,
    {
      variables: { userId },
      skip: !userId,
      fetchPolicy: 'network-only',
    }
  );

  const [createDriver, { loading: createLoading }] = useMutation(CREATE_DRIVER);

  const checkOrCreateDriver = async () => {
    try {
      if (!userId) {
        throw new Error('No user ID available');
      }

      // Check if driver already exists
      const existingCount = existingDriverData?.drivers_aggregate?.aggregate?.count || 0;
      if (existingCount > 0) {
        const driver = driverData?.drivers?.[0];
        if (driver?.verification_status === 'pending') {
          router.replace('/(main)/pending-approval');
        } else {
          router.replace('/(main)');
        }
        return { success: true };
      }

      // Create new driver if doesn't exist
      const { data: newDriver } = await createDriver({
        variables: { userId }
      });

      if (newDriver?.insert_drivers_one?.id) {
        await refetch();
        router.replace('/(main)/onboarding');
        return { success: true };
      }

      return { error: 'Failed to create driver profile' };
    } catch (error) {
      console.error('Error in checkOrCreateDriver:', error);
      return { error: error instanceof Error ? error.message : 'An unknown error occurred' };
    }
  };

  return {
    driver: driverData?.drivers?.[0],
    loading: driverLoading || createLoading,
    error: driverError,
    checkOrCreateDriver,
    refetchDriver: refetch,
  };
}

export default useDriver;
