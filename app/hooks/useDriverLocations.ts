import { useQuery } from '@apollo/client';
import { gql } from '@apollo/client';
import { useUserId } from '@nhost/react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

const RADIUS = 5000;
const DEFAULT_LAST_SEEN_SECONDS = 120;

const GET_APP_SETTINGS = gql`
  query GetAppSettings {
    app_settings {
      driver_search_radius
      driver_last_seen_seconds
      verification_types
    }
  }
`;

const DRIVER_LOCATIONS_QUERY = gql`
  query GetDriverLocations(
    $lastMinute: timestamptz!
    $location: geography!
    $distance: Float!
    $verificationTypes: [document_status_enum!]
  ) {
    driver_locations_current(
      where: {
        _and: [
          {
            driver: {
              is_active: { _eq: true }
              is_busy: { _eq: false }
              verification_status: { _in: $verificationTypes }
            }
          }
          { updated_at: { _gte: $lastMinute } }
          { location: { _st_d_within: { distance: $distance, from: $location } } }
        ]
      }
      order_by: { updated_at: desc }
    ) {
      id
      driver_id
      location
      heading
      speed
      battery_level
      updated_at
      driver {
        id
        is_active
        name
        phone_number
        local_unit
        user {
          id
          displayName
        }
        vehicles {
          id
          license_plate
          model
          color
          make
          vehicle_type
        }
        average_rating
        ratings_aggregate {
          aggregate {
            avg {
              rating
            }
          }
        }
        completedRides: free_rides_aggregate(where: { status: { _eq: "completed" } }) {
          aggregate {
            count
          }
        }
        totalRides: free_rides_aggregate {
          aggregate {
            count
          }
        }
      }
    }
  }
`;

const createTimestamp = (seconds: number): string => {
  const date = new Date();
  date.setSeconds(date.getSeconds() - seconds);
  return date.toISOString();
};

export function useDriverLocations(
  pollInterval = 30000,
  [latitude, longitude]: [number, number],
  isSkip = false
) {
  const userId = useUserId();
  const prevDriverLocations = useRef<any[]>([]);

  const { data: settingsData } = useQuery(GET_APP_SETTINGS, {
    fetchPolicy: 'cache-first',
    nextFetchPolicy: 'cache-and-network',
  });

  const settings = useMemo(() => {
    const appSettings = settingsData?.app_settings[0];
    return {
      searchRadius: appSettings?.driver_search_radius ?? RADIUS,
      lastSeenSeconds: appSettings?.driver_last_seen_seconds ?? DEFAULT_LAST_SEEN_SECONDS,
      verificationTypes: appSettings?.verification_types ?? [],
    };
  }, [settingsData?.app_settings[0]]);

  const location = useMemo(
    () => ({
      type: 'Point' as const,
      coordinates: [longitude, latitude] as [number, number],
    }),
    [latitude, longitude]
  );

  const [lastMinute, setLastMinute] = useState(() => createTimestamp(settings.lastSeenSeconds));

  const updateLastMinute = useCallback(() => {
    setLastMinute(createTimestamp(settings.lastSeenSeconds));
  }, [settings.lastSeenSeconds]);

  useEffect(() => {
    if (!userId) return;

    updateLastMinute();
    const intervalId = setInterval(updateLastMinute, pollInterval);
    return () => clearInterval(intervalId);
  }, [userId, pollInterval, updateLastMinute]);

  const { data, loading, error, refetch } = useQuery(DRIVER_LOCATIONS_QUERY, {
    skip: isSkip || !latitude || !longitude,
    variables: {
      lastMinute,
      location,
      distance: settings.searchRadius,
      verificationTypes: settings.verificationTypes,
    },
    pollInterval,
    fetchPolicy: 'cache-and-network',
    notifyOnNetworkStatusChange: true,
    // onError: error => console.error('Error fetching driver locations:', error),
  });

  const driverLocations = useMemo(() => {
    const currentLocations = data?.driver_locations_current ?? [];

    if (loading && prevDriverLocations.current.length > 0) {
      return prevDriverLocations.current;
    }

    if (JSON.stringify(currentLocations) !== JSON.stringify(prevDriverLocations.current)) {
      prevDriverLocations.current = currentLocations;
      return currentLocations;
    }

    return prevDriverLocations.current;
  }, [data?.driver_locations_current, loading]);

  return {
    driverLocations,
    loading: loading && prevDriverLocations.current.length === 0,
    error,
    refetch,
  };
}

export default useDriverLocations;
