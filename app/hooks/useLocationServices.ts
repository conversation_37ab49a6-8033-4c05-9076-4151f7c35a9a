import { useState, useRef, useCallback, useMemo } from 'react';
import * as Location from 'expo-location';
import { Alert } from 'react-native';
import { router } from 'expo-router';

const useLocationServices = () => {
  const [currentLocation, setCurrentLocation] = useState<Location.LocationObject | null>(null);
  const locationSubscription = useRef<Location.LocationSubscription | null>(null);

  // Initialize location tracking when the hook mounts
  // useEffect(() => {
  //   const initializeLocation = async () => {
  //     try {
  //       const { status } = await Location.requestForegroundPermissionsAsync();
  //       if (status !== 'granted') {
  //         console.error('Location permission denied');
  //         return;
  //       }

  //       // Get initial location
  //       const location = await Location.getCurrentPositionAsync({
  //         accuracy: Location.Accuracy.Balanced
  //       });
  //       setCurrentLocation(location);

  //       // Start watching location
  //       locationSubscription.current = await Location.watchPositionAsync(
  //         {
  //           accuracy: Location.Accuracy.Balanced,
  //           timeInterval: 1000,
  //           distanceInterval: 10,
  //         },
  //         (newLocation) => {
  //           setCurrentLocation(newLocation);
  //         }
  //       );
  //     } catch (error) {
  //       console.error('Error initializing location:', error);
  //     }
  //   };

  //   initializeLocation();

  //   return () => {
  //     if (locationSubscription.current) {
  //       locationSubscription.current.remove();
  //     }
  //   };
  // }, []);

  const requestPermissions = useCallback(async () => {
    try {
      const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();
      if (foregroundStatus !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Foreground location permission is required to show your location on the map.',
          [{ text: 'OK' }]
        );
        return false;
      }

      // const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();
      // if (backgroundStatus !== 'granted') {
      //   Alert.alert(
      //     'Permission Required',
      //     'Background location permission is required to track your location while driving.',
      //     [{ text: 'OK' }]
      //   );
      //   return false;
      // }

      return true;
    } catch (error) {
      console.error('Error requesting permissions:', error);
      return false;
    }
  }, []);

  const startForegroundTracking = useCallback(async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.error('Foreground location permission denied');
        return false;
      }
      // console.log(Location.LocationAccuracy);

      // const initialLocation = await Location.getCurrentPositionAsync({
      //   accuracy: Location.Accuracy.BestForNavigation
      // });
      // setCurrentLocation(initialLocation);

      locationSubscription.current = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.BestForNavigation,
          timeInterval: 5000,
          distanceInterval: 10,
        },
        newLocation => {
          console.log('New location:', newLocation);
          setCurrentLocation(newLocation);
        }
      );

      return true;
    } catch (error) {
      router.replace('/(main)/request-permissions');
      // console.error('Error requesting permissions:', error);
      return false;
    }
  }, [setCurrentLocation]);

  const stopForegroundTracking = useCallback(() => {
    // Remove the location tracking subscription if it exists
    if (locationSubscription.current) {
      locationSubscription.current.remove();
      locationSubscription.current = null;
    }
    // Reset current location
    setCurrentLocation(null);
  }, [setCurrentLocation]);

  const returnValue = useMemo(
    () => ({
      // isBackgroundTracking,
      currentLocation,
      requestPermissions,
      startForegroundTracking,
      stopForegroundTracking,
    }),
    [
      // isBackgroundTracking,
      currentLocation,
      requestPermissions,
      startForegroundTracking,
      stopForegroundTracking,
    ]
  );

  return returnValue;
};

export default useLocationServices;
