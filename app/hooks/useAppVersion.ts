import { Platform, Linking, Alert } from 'react-native';
import { gql, useQuery } from '@apollo/client';
import * as Semver from 'semver';
import Constants from 'expo-constants';
import { useTranslation } from 'react-i18next';
import { LINKS } from '@/constants';
import { useState, useCallback, useEffect } from 'react';

const GET_APP_VERSION = gql`
  query GetAppVersion($platform: String!) {
    app_versions(where: { platform: { _eq: $platform }, app_for: { _eq: "user" } }) {
      id
      latest_version
      min_version
      is_critical_update
    }
  }
`;

const useAppVersion = (userId: string | undefined) => {
  const { t } = useTranslation();
  const [forceUpdateRequired, setForceUpdateRequired] = useState(false);

  const {
    data: versionData,
    loading: versionLoading,
    error: versionError,
  } = useQuery(GET_APP_VERSION, {
    variables: { platform: Platform.OS },
    fetchPolicy: 'network-only',
    skip: !userId,
    // onCompleted: data => {
    //   console.log('Version data received:', data);
    // },
    // onError: error => {
    //   console.error('Error fetching app version:', error);
    // },
  });

  const openAppStore = useCallback(async () => {
    const storeUrl = Platform.OS === 'ios' ? LINKS.iosApp : LINKS.passengerApp;

    try {
      const supported = await Linking.canOpenURL(storeUrl);
      if (supported) {
        await Linking.openURL(storeUrl);
      } else {
        Alert.alert(t('common.error'), t('update.error.store'));
      }
    } catch (error) {
      console.error('Error opening store:', error);
      Alert.alert(t('common.error'), t('update.error.store'));
    }
  }, [t]);

  const showForceUpdateDialog = useCallback(
    (versionInfo: any) => {
      Alert.alert(
        t('update.required.title'),
        versionInfo?.update_message || t('update.required.message'),
        [
          {
            text: t('update.button.update'),
            onPress: () => {
              openAppStore();
              setTimeout(() => showForceUpdateDialog(versionInfo), 10000);
            },
          },
        ],
        {
          cancelable: false,
          // onDismiss: () => showForceUpdateDialog(versionInfo),
        }
      );
    },
    [t, openAppStore]
  );

  const showOptionalUpdateDialog = useCallback(
    (versionInfo: any) => {
      Alert.alert(
        t('update.available.title'),
        versionInfo?.update_message || t('update.available.message'),
        [
          {
            text: t('update.button.update'),
            onPress: () => openAppStore(),
          },
          {
            text: t('update.button.later'),
            style: 'cancel',
          },
        ]
      );
    },
    [t, openAppStore]
  );

  const checkAppVersion = useCallback(async () => {
    try {
      const currentVersion = Constants.expoConfig?.version || '1.0.0';
      const versionInfo = versionData?.app_versions[0];

      if (!versionInfo) {
        return;
      }

      const needsUpdate = Semver.lt(currentVersion, versionInfo.min_version);
      const updateAvailable = Semver.lt(currentVersion, versionInfo.latest_version);

      if (needsUpdate && versionInfo?.is_critical_update) {
        setForceUpdateRequired(true);
        showForceUpdateDialog(versionInfo);
      } else if (updateAvailable) {
        showOptionalUpdateDialog(versionInfo);
      }
    } catch (error) {
      console.error('Failed to check app version:', error);
    } finally {
    }
  }, [versionData, showForceUpdateDialog, showOptionalUpdateDialog]);

  useEffect(() => {
    if (versionData && !versionError) {
      checkAppVersion();
    }
  }, [versionData, versionError, checkAppVersion]);

  return {
    versionLoading,
    forceUpdateRequired,
  };
};

export default useAppVersion;
