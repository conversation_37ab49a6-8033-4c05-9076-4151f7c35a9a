import { useMutation } from '@apollo/client';
import { gql } from '@apollo/client';


const CREATE_RIDE = gql`
  mutation CreateRide($input: ride_insert_input!) {
    insert_rides_one(object: $input) {
      id
      status
      pickup_location
      driver {
        id
        name
      }
      created_at
    }
  }
`;



export const useCreateRide = () => {
  return useMutation(CREATE_RIDE);
};

export default useCreateRide;