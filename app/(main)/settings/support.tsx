import React from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { Text, Surface, IconButton, Button, Card } from 'react-native-paper';
import { router } from 'expo-router';
import { useTheme } from '@/theme/ThemeContext';
import {
  handleCallSupport,
  handleEmailSupport,
  handleWhatsAppSupport,
} from 'app/utils/social-support';

export default function SupportScreen() {
  const { theme } = useTheme();

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      contentContainerStyle={styles.content}
    >
      <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]} elevation={2}>
        <View style={styles.headerLeft}>
          <IconButton
            icon="arrow-left"
            size={24}
            iconColor={theme.colors.primary}
            onPress={() => router.back()}
          />
          <Text
            variant="titleLarge"
            style={[styles.headerTitle, { color: theme.colors.onSurface }]}
          >
            Support
          </Text>
        </View>
      </Surface>

      <View style={styles.cardsContainer}>
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }]} elevation={2}>
          <Card.Content>
            <Text
              variant="titleMedium"
              style={[styles.cardTitle, { color: theme.colors.onSurface }]}
            >
              Contact Support
            </Text>
            <Text
              variant="bodyMedium"
              style={[styles.cardDescription, { color: theme.colors.onSurfaceVariant }]}
            >
              Our support team is available 24/7 to assist you with any issues or questions.
            </Text>
            <View style={styles.contactButtons}>
              <Button
                mode="contained"
                onPress={handleCallSupport}
                style={[styles.button, { backgroundColor: theme.colors.primary }]}
                icon="phone"
              >
                Call Support
              </Button>
              <Button
                mode="contained-tonal"
                onPress={handleWhatsAppSupport}
                style={[styles.button, { backgroundColor: theme.colors.primary }]}
                icon="whatsapp"
              >
                WhatsApp Support
              </Button>
              <Button
                mode="outlined"
                onPress={handleEmailSupport}
                style={[styles.button, { borderColor: theme.colors.primary }]}
                textColor={theme.colors.primary}
                icon="email"
              >
                Email Support
              </Button>
            </View>
          </Card.Content>
        </Card>

        {/* <Card style={[styles.card, { backgroundColor: theme.colors.surface }]} elevation={2}>
          <Card.Content>
            <Text variant="titleMedium" style={[styles.cardTitle, { color: theme.colors.onSurface }]}>
              FAQs
            </Text>
            <Text variant="bodyMedium" style={[styles.cardDescription, { color: theme.colors.onSurfaceVariant }]}>
              Find quick answers to common questions about our service.
            </Text>
            <Button
              mode="outlined"
              onPress={() => router.push('/(main)/settings/help')}
              style={[styles.button, { borderColor: theme.colors.primary }]}
              textColor={theme.colors.primary}
              icon="help-circle"
            >
              View FAQs
            </Button>
          </Card.Content>
        </Card> */}

        <Card style={[styles.card, { backgroundColor: theme.colors.surface }]} elevation={2}>
          <Card.Content>
            <Text
              variant="titleMedium"
              style={[styles.cardTitle, { color: theme.colors.onSurface }]}
            >
              Legal Information
            </Text>
            <Text
              variant="bodyMedium"
              style={[styles.cardDescription, { color: theme.colors.onSurfaceVariant }]}
            >
              View our terms of service and privacy policy.
            </Text>
            <View style={styles.legalButtons}>
              <Button
                mode="outlined"
                onPress={() => router.push('/(main)/settings/terms')}
                style={[styles.button, { borderColor: theme.colors.primary }]}
                textColor={theme.colors.primary}
                icon="file-document"
              >
                Terms of Service
              </Button>
              <Button
                mode="outlined"
                onPress={() => router.push('/(main)/settings/privacy')}
                style={[styles.button, { borderColor: theme.colors.primary }]}
                textColor={theme.colors.primary}
                icon="shield-account"
              >
                Privacy Policy
              </Button>
            </View>
          </Card.Content>
        </Card>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  header: {
    padding: 8,
    borderRadius: 12,
    marginBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    marginLeft: 8,
    fontWeight: '600',
  },
  cardsContainer: {
    gap: 16,
  },
  card: {
    borderRadius: 12,
  },
  cardTitle: {
    marginBottom: 8,
    fontWeight: '600',
  },
  cardDescription: {
    marginBottom: 16,
  },
  contactButtons: {
    gap: 12,
  },
  legalButtons: {
    gap: 12,
  },
  button: {
    borderRadius: 8,
  },
});
