import React from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { Text, Surface, IconButton, List, Switch } from 'react-native-paper';
import { router } from 'expo-router';
import { useTheme } from '../../../src/theme/ThemeContext';

export default function PrivacyPolicyScreen() {
  const { theme } = useTheme();
  const [locationTracking, setLocationTracking] = React.useState(true);

  const privacyContent = [
    {
      title: 'Information We Collect',
      content: 'We collect information that you provide directly to us, such as your name, email address, phone number, and driver\'s license information. We also collect information about your vehicle and your use of our services.'
    },
    {
      title: 'How We Use Your Information',
      content: 'We use the information we collect to provide and improve our services, communicate with you, and comply with legal obligations. This includes processing your payments, verifying your identity, and optimizing our app performance.'
    },
    {
      title: 'Information Sharing',
      content: 'We may share your information with third parties only as necessary to provide our services or as required by law. We do not sell your personal information to advertisers or other third parties.'
    }
  ];

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      contentContainerStyle={styles.content}
    >
      <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]} elevation={2}>
        <View style={styles.headerLeft}>
          <IconButton
            icon="arrow-left"
            size={24}
            iconColor={theme.colors.primary}
            onPress={() => router.back()}
          />
          <Text variant="titleLarge" style={[styles.headerTitle, { color: theme.colors.onSurface }]}>Privacy & Data</Text>
        </View>
      </Surface>

      <Surface style={[styles.settingsCard, { backgroundColor: theme.colors.surface }]} elevation={2}>
        <List.Section>
          <List.Subheader style={[styles.sectionTitle, { color: theme.colors.primary }]}>
            Privacy Settings
          </List.Subheader>
          <List.Item
            title="Location Tracking"
            description="Required for ride tracking and navigation"
            left={props => <List.Icon {...props} icon="map-marker" color={theme.colors.primary} />}
            right={() => (
              <Switch
                value={locationTracking}
                onValueChange={setLocationTracking}
                disabled
              />
            )}
            titleStyle={{ color: theme.colors.onSurface }}
            descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
          />
        </List.Section>
      </Surface>

      {privacyContent.map((section, index) => (
        <Surface 
          key={index} 
          style={[styles.contentCard, { backgroundColor: theme.colors.surface }]} 
          elevation={2}
        >
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.primary }]}>
            {section.title}
          </Text>
          <Text style={[styles.sectionContent, { color: theme.colors.onSurface }]}>
            {section.content}
          </Text>
        </Surface>
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  header: {
    padding: 8,
    borderRadius: 12,
    marginBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    marginLeft: 8,
  },
  settingsCard: {
    marginBottom: 16,
    borderRadius: 8,
  },
  contentCard: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  sectionContent: {
    fontSize: 14,
    lineHeight: 20,
  },
});
