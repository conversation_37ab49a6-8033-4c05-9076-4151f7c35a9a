import React, { useState } from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { Text, Surface, TextInput, Button, IconButton, List, Chip } from 'react-native-paper';
import { router } from 'expo-router';

export default function ContactSupportScreen() {
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');

  const supportTopics = [
    { title: 'Account Issues', icon: 'account-alert' },
    { title: 'Payment Problems', icon: 'cash-alert' },
    { title: 'App Technical Issues', icon: 'cellphone-alert' },
    { title: 'Vehicle Related', icon: 'car-alert' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <IconButton
          icon="arrow-left"
          size={24}
          onPress={() => router.back()}
        />
        <Text variant="titleLarge" style={styles.headerTitle}>Contact Support</Text>
      </View>

      <Surface style={styles.quickHelpSection} elevation={1}>
        <Text variant="titleMedium" style={styles.sectionTitle}>Quick Help</Text>
        <View style={styles.topicsGrid}>
          {supportTopics.map((topic, index) => (
            <Surface key={index} style={styles.topicCard} elevation={2}>
              <IconButton
                icon={topic.icon}
                size={24}
                onPress={() => setSubject(topic.title)}
              />
              <Text variant="labelSmall" style={styles.topicTitle}>{topic.title}</Text>
            </Surface>
          ))}
        </View>
      </Surface>

      <Surface style={styles.contactForm} elevation={1}>
        <Text variant="titleMedium" style={styles.sectionTitle}>Send Message</Text>
        
        <TextInput
          label="Subject"
          value={subject}
          onChangeText={setSubject}
          mode="outlined"
          style={styles.input}
        />
        
        <TextInput
          label="Message"
          value={message}
          onChangeText={setMessage}
          mode="outlined"
          multiline
          numberOfLines={6}
          style={styles.input}
        />

        <Button
          mode="contained"
          onPress={() => {}}
          style={styles.submitButton}
        >
          Send Message
        </Button>
      </Surface>

      <Surface style={styles.alternativeSection} elevation={1}>
        <List.Item
          title="Emergency Support"
          description="24/7 Phone Support"
          left={props => <List.Icon {...props} icon="phone" />}
          right={() => (
            <Chip mode="outlined" style={styles.statusChip}>
              Available
            </Chip>
          )}
          onPress={() => {}}
        />
        <List.Item
          title="Live Chat"
          description="Chat with support agent"
          left={props => <List.Icon {...props} icon="message-processing" />}
          right={() => (
            <Chip mode="outlined" style={styles.statusChip}>
              Online
            </Chip>
          )}
          onPress={() => {}}
        />
      </Surface>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
  },
  headerTitle: {
    marginLeft: 8,
    fontWeight: '600',
  },
  quickHelpSection: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#fff',
  },
  sectionTitle: {
    marginBottom: 16,
    fontWeight: '600',
  },
  topicsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  topicCard: {
    width: '48%',
    alignItems: 'center',
    padding: 12,
    marginBottom: 12,
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  topicTitle: {
    marginTop: 4,
    textAlign: 'center',
  },
  contactForm: {
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#fff',
  },
  input: {
    marginBottom: 16,
  },
  submitButton: {
    marginTop: 8,
  },
  alternativeSection: {
    margin: 16,
    marginTop: 0,
    borderRadius: 12,
    backgroundColor: '#fff',
    overflow: 'hidden',
  },
  statusChip: {
    backgroundColor: 'transparent',
  },
});
