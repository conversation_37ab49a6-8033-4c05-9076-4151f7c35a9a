import React from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { Text, Surface, IconButton, List, Button } from 'react-native-paper';
import { router } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useUserId } from '@nhost/react';
import { useQuery } from '@apollo/client';
import { GET_DRIVER } from '../../graphql/driver';
import { useTheme } from '../../../src/theme/ThemeContext';

export default function VehicleSettingsScreen() {
  const userId = useUserId();
  const { theme } = useTheme();
  const { data, loading } = useQuery(GET_DRIVER, {
    variables: { userId },
    skip: !userId,
  });

  const vehicle = data?.drivers[0]?.vehicles[0];

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      contentContainerStyle={styles.content}
    >
      <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]} elevation={2}>
        <View style={styles.headerLeft}>
          <IconButton
            icon="arrow-left"
            size={24}
            iconColor={theme.colors.primary}
            onPress={() => router.back()}
          />
          <Text variant="titleLarge" style={[styles.headerTitle, { color: theme.colors.onSurface }]}>
            Vehicle Information
          </Text>
        </View>
      </Surface>

      <Surface style={[styles.vehicleCard, { backgroundColor: theme.colors.surface }]} elevation={2}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={{ color: theme.colors.onSurfaceVariant }}>Loading vehicle information...</Text>
          </View>
        ) : vehicle ? (
          <View>
            <List.Section>
              <List.Item
                title="Vehicle Type"
                description={vehicle.type === 'auto' ? 'Autom-Rickshaw' : 'E-Rickshaw'}
                left={props => <List.Icon {...props} icon="car" color={theme.colors.primary} />}
                titleStyle={{ color: theme.colors.onSurface }}
                descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
              />
              <List.Item
                title="Make"
                description={vehicle.make}
                left={props => <List.Icon {...props} icon="factory" color={theme.colors.primary} />}
                titleStyle={{ color: theme.colors.onSurface }}
                descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
              />
              <List.Item
                title="Model"
                description={vehicle.model}
                left={props => <List.Icon {...props} icon="car-info" color={theme.colors.primary} />}
                titleStyle={{ color: theme.colors.onSurface }}
                descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
              />
              <List.Item
                title="Year"
                description={vehicle.year}
                left={props => <List.Icon {...props} icon="calendar" color={theme.colors.primary} />}
                titleStyle={{ color: theme.colors.onSurface }}
                descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
              />
              <List.Item
                title="Color"
                description={vehicle.color}
                left={props => <List.Icon {...props} icon="palette" color={theme.colors.primary} />}
                titleStyle={{ color: theme.colors.onSurface }}
                descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
              />
              <List.Item
                title="License Plate"
                description={vehicle.license_plate}
                left={props => <List.Icon {...props} icon="card-text" color={theme.colors.primary} />}
                titleStyle={{ color: theme.colors.onSurface }}
                descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
              />
            </List.Section>


          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, { color: theme.colors.onSurfaceVariant }]}>
              No vehicle information found
            </Text>
            <Button
              mode="contained"
              onPress={() => router.push('/(main)/onboarding')}
              style={[styles.button, { backgroundColor: theme.colors.primary }]}
              icon="plus"
            >
              Add Vehicle Information
            </Button>
          </View>
        )}
      </Surface>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  header: {
    padding: 8,
    borderRadius: 12,
    marginBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    marginLeft: 8,
    fontWeight: '600',
  },
  vehicleCard: {
    borderRadius: 12,
    padding: 16,
  },
  loadingContainer: {
    padding: 24,
    alignItems: 'center',
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
    gap: 16,
  },
  emptyText: {
    textAlign: 'center',
    marginBottom: 16,
  },
  buttonContainer: {
    marginTop: 24,
  },
  button: {
    borderRadius: 8,
  },
});
