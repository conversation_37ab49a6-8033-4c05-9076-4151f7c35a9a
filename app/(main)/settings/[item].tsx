import React from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { Text, Surface, IconButton } from 'react-native-paper';
import { router, useLocalSearchParams } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';

const COLORS = {
  primary: '#00897B', // Teal 600
  secondary: '#26A69A', // Teal 400
  accent: '#4DB6AC', // Teal 300
  background: '#E0F2F1', // Teal 50
  surface: '#FFFFFF',
  text: '#004D40', // Teal 900
  error: '#FF5252',
};

const SETTINGS_ITEMS: Record<string, { title: string; icon: string }> = {
  profile: {
    title: 'Profile',
    icon: 'account',
  },
  vehicle: {
    title: 'Vehicle Information',
    icon: 'car',
  },
  documents: {
    title: 'Documents',
    icon: 'file-document',
  },
  notifications: {
    title: 'Notifications',
    icon: 'bell',
  },
  language: {
    title: 'Language',
    icon: 'translate',
  },
  help: {
    title: 'Help Center',
    icon: 'help-circle',
  },
  about: {
    title: 'About',
    icon: 'information',
  },
};

export default function SettingsItemScreen() {
  const { item } = useLocalSearchParams();
  const settingsItem = SETTINGS_ITEMS[item as keyof typeof SETTINGS_ITEMS];

  if (!settingsItem) {
    return null;
  }

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: COLORS.background }]}
      contentContainerStyle={styles.scrollContent}
    >
      <View style={styles.header}>
        <Surface style={styles.headerCard} elevation={4}>
          <View style={styles.headerLeft}>
            <IconButton
              icon="arrow-left"
              size={24}
              iconColor={COLORS.primary}
              onPress={() => router.back()}
            />
            <MaterialCommunityIcons
              name={settingsItem?.icon}
              size={24}
              color={COLORS.primary}
              style={styles.headerIcon}
            />
            <Text variant="titleLarge" style={styles.headerTitle}>
              {settingsItem?.title}
            </Text>
          </View>
        </Surface>
      </View>

      <Surface style={styles.contentCard} elevation={2}>
        <Text variant="bodyMedium" style={styles.placeholder}>
          {settingsItem?.title} settings will be implemented here.
        </Text>
      </Surface>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  headerCard: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: COLORS.surface,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: 8,
  },
  headerTitle: {
    color: COLORS.text,
    fontWeight: '700',
  },
  contentCard: {
    borderRadius: 12,
    backgroundColor: COLORS.surface,
    padding: 16,
    marginBottom: 16,
  },
  placeholder: {
    color: COLORS.text,
    opacity: 0.6,
    textAlign: 'center',
  },
});
