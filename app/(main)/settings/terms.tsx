import React from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { Text, Surface, IconButton, List } from 'react-native-paper';
import { router } from 'expo-router';
import { useTheme } from '../../../src/theme/ThemeContext';

export default function TermsScreen() {
  const { theme } = useTheme();

  const termsContent = [
    {
      title: 'Service Agreement',
      content: 'By using our driver services, you agree to comply with all applicable laws and regulations. You must maintain valid licenses, permits, and insurance as required by local authorities.'
    },
    {
      title: 'Driver Responsibilities',
      content: 'As a driver, you are responsible for maintaining your vehicle in good condition, following traffic laws, and providing professional service to customers. You must complete trips efficiently and safely.'
    },
    {
      title: 'Payment Terms',
      content: 'Payments are processed according to our payment schedule. Service fees and commissions will be deducted from your earnings. You are responsible for all taxes on your income.'
    },
    {
      title: 'Safety & Compliance',
      content: 'You must follow all safety protocols, including COVID-19 guidelines. Maintain appropriate insurance coverage and report any incidents immediately.'
    }
  ];

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      contentContainerStyle={styles.content}
    >
      <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]} elevation={2}>
        <View style={styles.headerLeft}>
          <IconButton
            icon="arrow-left"
            size={24}
            iconColor={theme.colors.primary}
            onPress={() => router.back()}
          />
          <Text variant="titleLarge" style={[styles.headerTitle, { color: theme.colors.onSurface }]}>Terms of Service</Text>
        </View>
      </Surface>

      <Surface style={[styles.versionCard, { backgroundColor: theme.colors.surface }]} elevation={2}>
        <List.Item
          title="Terms Version"
          description="Last updated: January 2024"
          left={props => <List.Icon {...props} icon="file-document" color={theme.colors.primary} />}
          titleStyle={{ color: theme.colors.onSurface }}
          descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
        />
      </Surface>

      {termsContent.map((section, index) => (
        <Surface 
          key={index} 
          style={[styles.contentCard, { backgroundColor: theme.colors.surface }]} 
          elevation={2}
        >
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.primary }]}>
            {section.title}
          </Text>
          <Text style={[styles.sectionContent, { color: theme.colors.onSurface }]}>
            {section.content}
          </Text>
        </Surface>
      ))}

      <Surface style={[styles.actionCard, { backgroundColor: theme.colors.surface }]} elevation={2}>
        <List.Item
          title="Download Full Terms"
          description="View complete terms document"
          left={props => <List.Icon {...props} icon="download" color={theme.colors.primary} />}
          onPress={() => {}}
          titleStyle={{ color: theme.colors.onSurface }}
          descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
        />
      </Surface>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  header: {
    padding: 8,
    borderRadius: 12,
    marginBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    marginLeft: 8,
  },
  versionCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  contentCard: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
  },
  actionCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  sectionContent: {
    fontSize: 14,
    lineHeight: 20,
  },
});
