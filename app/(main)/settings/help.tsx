import React from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { List, Text, Surface, IconButton, Searchbar } from 'react-native-paper';
import { router } from 'expo-router';
import { useTheme } from '../../../src/theme/ThemeContext';
import { MaterialCommunityIcons } from '@expo/vector-icons';

export default function HelpCenterScreen() {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = React.useState('');

  const faqCategories = [
    {
      title: 'Getting Started',
      icon: 'rocket',
      items: [
        {
          title: 'How to start driving',
          description: 'Learn how to begin your journey as a driver',
        },
        { title: 'Account setup', description: 'Complete your profile and documentation' },
        { title: 'Payment setup', description: 'Set up your payment methods and earnings' },
      ],
    },
    {
      title: 'Daily Operations',
      icon: 'car',
      items: [
        { title: 'Using the app', description: 'Navigate the features of the app effectively' },
        { title: 'Location tracking', description: 'Understand how location services work' },
        { title: 'Earnings and payments', description: 'Track your earnings and get paid' },
      ],
    },
    {
      title: 'Account & Security',
      icon: 'shield-account',
      items: [
        { title: 'Account settings', description: 'Manage your account preferences' },
        { title: 'Security features', description: 'Keep your account secure' },
        { title: 'Privacy settings', description: 'Control your data and privacy' },
      ],
    },
  ];

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      contentContainerStyle={styles.content}
    >
      <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]} elevation={2}>
        <View style={styles.headerLeft}>
          <IconButton
            icon="arrow-left"
            size={24}
            iconColor={theme.colors.primary}
            onPress={() => router.back()}
          />
          <Text
            variant="titleLarge"
            style={[styles.headerTitle, { color: theme.colors.onSurface }]}
          >
            Help Center
          </Text>
        </View>
      </Surface>

      <Surface style={[styles.searchCard, { backgroundColor: theme.colors.surface }]} elevation={2}>
        <Searchbar
          placeholder="Search help articles"
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          iconColor={theme.colors.primary}
          theme={{ colors: { primary: theme.colors.primary } }}
        />
      </Surface>

      {faqCategories.map((category, index) => (
        <Surface
          key={index}
          style={[styles.categoryCard, { backgroundColor: theme.colors.surface }]}
          elevation={2}
        >
          <List.Section>
            <List.Subheader style={[styles.categoryTitle, { color: theme.colors.primary }]}>
              {category?.title}
            </List.Subheader>
            {category?.items.map((item, itemIndex) => (
              <List.Item
                key={itemIndex}
                title={item?.title}
                description={item?.description}
                left={props => (
                  <MaterialCommunityIcons
                    {...props}
                    name={category?.icon as 'rocket' | 'car' | 'shield-account'}
                    size={24}
                    color={theme.colors.primary}
                  />
                )}
                titleStyle={{ color: theme.colors.onSurface }}
                descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
                onPress={() => {}}
              />
            ))}
          </List.Section>
        </Surface>
      ))}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  header: {
    padding: 8,
    borderRadius: 12,
    marginBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    marginLeft: 8,
  },
  searchCard: {
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  searchBar: {
    elevation: 0,
    backgroundColor: 'transparent',
  },
  categoryCard: {
    marginBottom: 16,
    borderRadius: 8,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
});
