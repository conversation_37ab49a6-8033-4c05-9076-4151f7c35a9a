import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity } from 'react-native';
import { Text, Surface, TextInput, Button, IconButton, List, Chip } from 'react-native-paper';
import { router } from 'expo-router';
import { useTheme } from '../../../src/theme/ThemeContext';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Alert } from 'react-native';

export default function FeedbackScreen() {
  const [message, setMessage] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const { theme } = useTheme();

  const feedbackCategories = [
    { title: 'App Experience', icon: 'star-outline' },
    { title: 'Feature Request', icon: 'lightbulb-outline' },
    { title: 'Bug Report', icon: 'bug-outline' },
    { title: 'Improvement Suggestion', icon: 'arrow-up-circle-outline' },
  ];

  const handleSubmitFeedback = async () => {
    if (!message.trim()) {
      Alert.alert('Error', 'Please enter your feedback message');
      return;
    }

    try {
      // TODO: Replace with actual feedback submission logic (e.g., GraphQL mutation)
      // console.log('Feedback submitted:', {
      //   category: selectedCategory,
      //   message: message.trim()
      // });

      // Show success message
      Alert.alert(
        'Thank You',
        'Your feedback has been submitted successfully. We appreciate your input!',
        [
          {
            text: 'OK',
            onPress: () => {
              setMessage('');
              setSelectedCategory('');
            },
          },
        ]
      );
    } catch (error) {
      console.error('Feedback submission error:', error);
      Alert.alert('Error', 'Failed to submit feedback. Please try again.');
    }
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      contentContainerStyle={styles.scrollContent}
    >
      <View style={styles.header}>
        <Surface
          style={[styles.headerCard, { backgroundColor: theme.colors.surface }]}
          elevation={4}
        >
          <View style={styles.headerLeft}>
            <IconButton
              icon="arrow-left"
              size={24}
              iconColor={theme.colors.primary}
              onPress={() => router.back()}
            />
            <Text
              variant="titleLarge"
              style={[styles.headerTitle, { color: theme.colors.onSurface }]}
            >
              Feedback
            </Text>
          </View>
        </Surface>
      </View>

      <Surface
        style={[styles.feedbackSection, { backgroundColor: theme.colors.surface }]}
        elevation={1}
      >
        <Text
          variant="titleMedium"
          style={[styles.sectionTitle, { color: theme.colors.onSurface }]}
        >
          Feedback Categories
        </Text>
        <View style={styles.categoriesGrid}>
          {feedbackCategories.map((category, index) => (
            <Surface
              key={index}
              style={[
                styles.categoryCard,
                {
                  backgroundColor:
                    selectedCategory === category.title
                      ? theme.colors.primaryContainer
                      : theme.colors.surfaceVariant,
                },
              ]}
              elevation={2}
            >
              <TouchableOpacity
                onPress={() => setSelectedCategory(category.title)}
                style={styles.categoryTouchable}
              >
                <MaterialCommunityIcons
                  name={category?.icon as any}
                  size={24}
                  color={
                    selectedCategory === category.title
                      ? theme.colors.primary
                      : theme.colors.onSurfaceVariant
                  }
                />
                <Text
                  variant="labelSmall"
                  style={[
                    styles.categoryTitle,
                    {
                      color:
                        selectedCategory === category.title
                          ? theme.colors.primary
                          : theme.colors.onSurfaceVariant,
                    },
                  ]}
                >
                  {category.title}
                </Text>
              </TouchableOpacity>
            </Surface>
          ))}
        </View>
      </Surface>

      <Surface
        style={[styles.feedbackForm, { backgroundColor: theme.colors.surface }]}
        elevation={1}
      >
        <Text
          variant="titleMedium"
          style={[styles.sectionTitle, { color: theme.colors.onSurface }]}
        >
          Share Your Feedback
        </Text>

        <TextInput
          label={`Your Feedback ${selectedCategory ? `- ${selectedCategory}` : ''}`}
          value={message}
          onChangeText={setMessage}
          mode="outlined"
          multiline
          numberOfLines={6}
          style={styles.input}
          placeholderTextColor={theme.colors.onSurfaceVariant}
          placeholder="Tell us what you think..."
        />

        <Button
          mode="contained"
          onPress={handleSubmitFeedback}
          style={styles.submitButton}
          disabled={!message.trim()}
        >
          Submit Feedback
        </Button>
      </Surface>

      <Surface
        style={[styles.additionalSection, { backgroundColor: theme.colors.surface }]}
        elevation={1}
      >
        <List.Item
          title="Feedback History"
          description="View your past feedback"
          left={props => <List.Icon {...props} icon="history" color={theme.colors.primary} />}
          right={() => (
            <Chip mode="outlined" style={styles.statusChip}>
              Coming Soon
            </Chip>
          )}
          onPress={() => {}}
          titleStyle={{ color: theme.colors.onSurface }}
          descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
        />
      </Surface>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  header: {
    marginBottom: 16,
  },
  headerCard: {
    paddingVertical: 16,
    paddingHorizontal: 8,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  headerTitle: {
    fontWeight: '600',
  },
  sectionTitle: {
    marginBottom: 16,
    fontWeight: '600',
  },
  feedbackSection: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
  },
  categoriesGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  categoryCard: {
    width: '22%',
    aspectRatio: 1,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryTouchable: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryTitle: {
    marginTop: 8,
    textAlign: 'center',
  },
  feedbackForm: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
  },
  input: {
    marginBottom: 16,
  },
  submitButton: {
    marginTop: 8,
  },
  additionalSection: {
    margin: 16,
    borderRadius: 12,
  },
  statusChip: {
    alignSelf: 'center',
  },
});
