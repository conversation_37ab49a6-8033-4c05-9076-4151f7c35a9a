import React, { useState } from 'react';
import { StyleSheet, View, Sc<PERSON>View, Alert, TouchableOpacity } from 'react-native';
import {
  Text,
  Surface,
  IconButton,
  Button,
  Portal,
  Dialog,
  TextInput,
  ActivityIndicator,
} from 'react-native-paper';
import { router } from 'expo-router';
import { useUserId, useUserData, useSignOut } from '@nhost/react';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../../../src/theme/ThemeContext';
import { useQuery, useMutation, useApolloClient, gql } from '@apollo/client';
import { GET_USER_PROFILE } from 'app/graphql/passenger';
import { useTranslation } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNotifications } from 'app/contexts/NotificationContext';
import useLocationServices from 'app/hooks/useLocationServices';
import { COLORS } from '@/constants';

const GET_DELETION_REQUEST = gql`
  query GetDeletionRequest($profileId: uuid!) {
    account_deletion_request(
      where: { profile_id: { _eq: $profileId } }
      order_by: { created_at: desc }
      limit: 1
    ) {
      id
      created_at
      status
      reason
    }
  }
`;

const CREATE_DELETION_REQUEST = gql`
  mutation CreateDeletionRequest($profileId: uuid!, $reason: String) {
    insert_account_deletion_request_one(object: { profile_id: $profileId, reason: $reason }) {
      id
    }
  }
`;

export const UPDATE_PROFILE = gql`
  mutation UpdateProfile($profileId: uuid!, $data: user_profiles_set_input!) {
    update_user_profiles_by_pk(pk_columns: { id: $profileId }, _set: $data) {
      id
      # verification_status
      # profile
      # selfie
      # license
    }
  }
`;

export default function ProfileScreen() {
  const userData = useUserData();
  const { theme } = useTheme();
  const { t } = useTranslation();
  const userId = useUserId();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deletionLoading, setDeletionLoading] = useState(false);
  const [deleteReason, setDeleteReason] = useState('');
  const { signOut } = useSignOut();
  const apolloClient = useApolloClient();
  const { isNotificationsEnabled, disableNotifications } = useNotifications();
  const { stopForegroundTracking } = useLocationServices();

  const { data, loading, error, refetch } = useQuery(GET_USER_PROFILE, {
    variables: { userId },
    skip: !userId,
  });
  const profile = data?.user_profiles[0];

  const { data: deletionData, refetch: refetchDeletion } = useQuery(GET_DELETION_REQUEST, {
    variables: { profileId: profile?.id },
    skip: !profile?.id,
  });

  const [createDeletionRequest] = useMutation(CREATE_DELETION_REQUEST, {
    onCompleted: () => {
      refetchDeletion();
    },
  });

  const handleSignOut = async () => {
    try {
      if (isNotificationsEnabled) {
        await disableNotifications();
      }

      // Clear all storage
      await AsyncStorage.clear();

      // Stop location tracking
      stopForegroundTracking();

      // Clear Apollo cache
      await apolloClient.resetStore();

      // Sign out from Nhost
      const { error } = await signOut();

      if (error) {
        console.error('Log out error:', error);
        return;
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // if (loading) return <AnimatedLoadingScreen />;
  if (error) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: theme.colors.background }]}>
        <MaterialCommunityIcons name="alert-circle" size={48} color={theme.colors.error} />
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {t('profile.errorLoading')}
        </Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      contentContainerStyle={styles.content}
    >
      {/* Header */}
      <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]} elevation={2}>
        <View style={styles.headerLeft}>
          <IconButton
            icon="arrow-left"
            size={24}
            iconColor={theme.colors.primary}
            onPress={() => router.back()}
          />
          <Text
            variant="titleLarge"
            style={[styles.headerTitle, { color: theme.colors.onSurface }]}
          >
            {t('settings.profile')}
          </Text>
        </View>
      </Surface>

      {/* Personal Information */}
      <Surface style={[styles.infoCard, { backgroundColor: theme.colors.surface }]} elevation={2}>
        <Text
          variant="titleMedium"
          style={[styles.sectionTitle, { color: theme.colors.onSurface }]}
        >
          {t('profile.personalInfo')}
        </Text>

        <View style={styles.infoGrid}>
          <InfoItem
            icon="account"
            label={t('profile.profileName')}
            value={profile?.full_name}
            theme={theme}
          />
          <InfoItem
            icon="phone"
            label={t('profile.phoneNumber')}
            value={profile?.phone}
            theme={theme}
          />

          <InfoItem icon="email" label={t('profile.email')} value={userData?.email} theme={theme} />

          <InfoItem
            icon="water"
            label={t('settings.bloodGroup')}
            value={profile?.blood_group}
            theme={theme}
          />
        </View>
      </Surface>

      {/* Blood Group Consent */}
      <BloodGroupSection
        profile={profile}
        theme={theme}
        t={t}
        onUpdateConsent={(newValue: any) => {
          refetch();
        }}
      />

      {/* Account Actions */}
      <Surface
        style={[styles.actionsCard, { backgroundColor: theme.colors.surface }]}
        elevation={2}
      >
        <Text
          variant="titleMedium"
          style={[styles.sectionTitle, { color: theme.colors.onSurface }]}
        >
          {t('profile.accountActions')}
        </Text>

        <View style={styles.actionButtons}>
          {/* <Button
            mode="outlined"
            icon="account-edit"
            onPress={() => router.push('/(main)/settings/edit-profile')}
            style={styles.actionButton}
          >
            {t('profile.editProfile')}
          </Button>

          <Button
            mode="outlined"
            icon="logout"
            onPress={handleSignOut}
            style={styles.actionButton}
            textColor={theme.colors.error}
          >
            {t('profile.signOut')}
          </Button> */}

          <Button
            mode="outlined"
            icon="delete"
            onPress={() => setShowDeleteDialog(true)}
            style={styles.actionButton}
            textColor={theme.colors.error}
          >
            {t('profile.deleteAccount')}
          </Button>
        </View>
      </Surface>

      {/* Delete Account Dialog */}
      <Portal>
        <Dialog
          visible={showDeleteDialog}
          onDismiss={() => !deletionLoading && setShowDeleteDialog(false)}
        >
          <Dialog.Title>{t('profile.deleteAccountConfirmTitle')}</Dialog.Title>
          <Dialog.Content>
            <Text variant="bodyMedium" style={{ marginBottom: 16 }}>
              {t('profile.deleteAccountConfirmMessage')}
            </Text>
            <Text
              variant="bodyMedium"
              style={{ marginBottom: 16, color: theme.colors.onSurfaceVariant }}
            >
              {t('profile.deleteAccountReviewInfo')}
            </Text>
            <Text
              variant="bodyMedium"
              style={{ marginBottom: 8, color: theme.colors.onSurfaceVariant }}
            >
              {t('profile.deleteAccountDataInfo')}
            </Text>
            <TextInput
              mode="outlined"
              label={t('profile.deleteAccountReason')}
              value={deleteReason}
              onChangeText={setDeleteReason}
              multiline
              numberOfLines={3}
              style={{ marginTop: 16 }}
              placeholder={t('profile.deleteAccountReasonPlaceholder')}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowDeleteDialog(false)} disabled={deletionLoading}>
              {t('common.cancel')}
            </Button>
            <Button
              textColor={theme.colors.error}
              disabled={deletionLoading}
              onPress={async () => {
                try {
                  setDeletionLoading(true);

                  const { data, errors } = await createDeletionRequest({
                    variables: {
                      profileId: profile.id,
                      reason: deleteReason.trim() || null,
                    },
                  });

                  if (errors) {
                    throw new Error('Failed to create deletion request');
                  }

                  Alert.alert(
                    t('profile.deleteAccountRequestedTitle'),
                    t('profile.deleteAccountRequestedMessage'),
                    [
                      {
                        text: t('common.continue'),
                        style: 'cancel',
                        onPress: () => setShowDeleteDialog(false),
                      },
                      {
                        text: t('auth.logout'),
                        style: 'destructive',
                        onPress: () => {
                          setShowDeleteDialog(false);
                          handleSignOut();
                        },
                      },
                    ]
                  );
                } catch (error) {
                  Alert.alert(t('common.error'), t('profile.deleteAccountError'), [{ text: 'OK' }]);
                } finally {
                  setDeletionLoading(false);
                }
              }}
            >
              {deletionLoading ? (
                <ActivityIndicator size="small" color={theme.colors.error} />
              ) : (
                t('profile.requestDelete')
              )}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </ScrollView>
  );
}

// Helper component for info items
const InfoItem = ({ icon, label, value, theme }: any) => (
  <View style={styles.infoItem}>
    <MaterialCommunityIcons name={icon} size={24} color={theme.colors.primary} />
    <View style={styles.infoTextContainer}>
      <Text style={[styles.infoLabel, { color: theme.colors.onSurfaceVariant }]}>{label}</Text>
      <Text style={[styles.infoValue, { color: theme.colors.onSurface }]}>{value}</Text>
    </View>
  </View>
);

const BloodGroupSection = ({ profile, theme, t, onUpdateConsent }: any) => {
  const [updating, setUpdating] = useState(false);
  const [updateUserProfile] = useMutation(UPDATE_PROFILE, {
    onError: error => {
      console.error('Error updating blood group consent:', error);
    },
  });

  const handleConsentChange = async (newValue: boolean) => {
    setUpdating(true);
    try {
      await updateUserProfile({
        variables: {
          profileId: profile?.id,
          data: {
            blood_donation_consent: newValue,
          },
        },
      });
      onUpdateConsent?.(newValue);
    } catch (error) {
      console.error('Error updating blood group consent:', error);
    } finally {
      setUpdating(false);
    }
  };

  return (
    <Surface style={[styles.infoCard, { backgroundColor: theme.colors.surface }]} elevation={2}>
      <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
        {t('profile.bloodDonation.title')}
      </Text>

      <View style={styles.consentContainer}>
        <View style={styles.consentTextContainer}>
          <Text style={styles.consentText}>{t('profile.bloodDonation.description')}</Text>
        </View>
        <View style={styles.radioCards}>
          <TouchableOpacity
            style={[styles.radioCard, profile?.blood_donation_consent && styles.radioCardSelected]}
            onPress={() => handleConsentChange(true)}
            disabled={updating}
          >
            <View style={styles.radioCardContent}>
              <Text style={styles.radioCardLabel}>{t('common.yes')}</Text>
              <View
                style={[
                  styles.radioCircle,
                  profile?.blood_donation_consent && styles.radioCircleSelected,
                ]}
              >
                {profile?.blood_donation_consent && <View style={styles.radioInnerCircle} />}
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.radioCard, !profile?.blood_donation_consent && styles.radioCardSelected]}
            onPress={() => handleConsentChange(false)}
            disabled={updating}
          >
            <View style={styles.radioCardContent}>
              <Text style={styles.radioCardLabel}>{t('common.no')}</Text>
              <View
                style={[
                  styles.radioCircle,
                  !profile?.blood_donation_consent && styles.radioCircleSelected,
                ]}
              >
                {!profile?.blood_donation_consent && <View style={styles.radioInnerCircle} />}
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </View>

      {/* <View style={styles.bloodGroupInfo}>
        <Text style={[styles.infoLabel, { color: theme.colors.onSurfaceVariant }]}>
          {t('profile.currentBloodGroup')}
        </Text>
        <Text style={[styles.infoValue, { color: theme.colors.onSurface }]}>
          {profile.blood_group || t('profile.notSpecified')}
        </Text>
      </View> */}
    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
    gap: 16,
    paddingBottom: 32,
  },
  header: {
    padding: 8,
    borderRadius: 12,
    marginBottom: 8,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    marginLeft: 8,
    fontWeight: '600',
  },
  headerCard: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  headerContent: {
    padding: 16,
  },
  backButton: {
    position: 'absolute',
    top: 8,
    left: 8,
    zIndex: 1,
  },
  profileHeaderSection: {
    alignItems: 'center',
    marginTop: 32,
  },
  profileAvatar: {
    borderWidth: 4,
    borderColor: 'white',
  },
  profileInfo: {
    alignItems: 'center',
    marginTop: 16,
  },
  nameText: {
    fontWeight: '600',
  },
  emailText: {
    marginTop: 4,
    fontSize: 14,
  },
  infoCard: {
    borderRadius: 16,
    padding: 16,
  },
  actionsCard: {
    borderRadius: 16,
    padding: 16,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 16,
  },
  infoGrid: {
    gap: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  infoTextContainer: {
    marginLeft: 12,
  },
  infoLabel: {
    fontSize: 12,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 2,
  },
  actionButtons: {
    gap: 12,
  },
  actionButton: {
    borderRadius: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginTop: 16,
  },
  bloodGroupContainer: {
    marginTop: 8,
  },
  bloodGroupInfo: {
    marginBottom: 16,
  },
  consentDescription: {
    fontSize: 14,
    marginTop: 8,
    lineHeight: 20,
  },
  consentSwitch: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  consentLabel: {
    fontSize: 14,
    flex: 1,
    marginLeft: 12,
  },
  consentContainer: {
    // flexDirection: 'row',
    // alignItems: 'center',
    // justifyContent: 'space-between',
    marginBottom: 16,
  },
  consentTextContainer: {
    flex: 1,
    marginRight: 16,
  },
  consentText: {
    fontSize: 14,
    // color: COLORS.primary,
  },
  radioCards: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
    // marginBottom: 16,
  },
  radioCard: {
    flex: 1,
    // backgroundColor: '#f5f5f5',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    padding: 12,
  },
  radioCardSelected: {
    backgroundColor: `${COLORS.primary}15`,
    borderColor: COLORS.primary,
  },
  radioCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  radioCardLabel: {
    fontSize: 14,
    color: COLORS.text,
    fontWeight: '500',
  },
  radioCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioCircleSelected: {
    borderColor: COLORS.primary,
  },
  radioInnerCircle: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: COLORS.primary,
  },
});
