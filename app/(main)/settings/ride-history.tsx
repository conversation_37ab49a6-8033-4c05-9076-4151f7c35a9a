import React, { use<PERSON>allback, useEffect, useState, useMemo } from 'react';
import { StyleSheet, View, Platform, Linking } from 'react-native';
import { Text, Surface, IconButton, ActivityIndicator } from 'react-native-paper';
import { router } from 'expo-router';
import { useQuery } from '@apollo/client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme } from '../../../src/theme/ThemeContext';
import { GET_RIDE_HISTORY } from 'app/graphql/ride';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import RideItem from 'app/components/RideItem';
import { FlashList } from '@shopify/flash-list';

interface UserProfile {
  __typename: string;
  phone: string;
  full_name: string;
}

interface Driver {
  __typename: string;
  id: string;
  name: string;
  phone_number: string;
}

interface Ride {
  __typename: string;
  id: string;
  status: string;
  created_at: string;
  pickup_address: string;
  dropoff_address: string;
  cancel_reason?: string;
  user_profile: UserProfile;
  driver: Driver;
}

interface RideHistoryData {
  free_rides: Ride[];
  free_rides_aggregate: {
    aggregate: {
      count: number;
    };
  };
}

const ITEMS_PER_PAGE = 20;
const ESTIMATED_ITEM_SIZE = 200;

const handleCall = (phoneNumber: string) => {
  const url = Platform.select({
    ios: `tel:${phoneNumber}`,
    android: `tel:${phoneNumber}`,
  });
  Linking.openURL(url as string);
};

export default function RideHistoryScreen() {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const [profileId, setProfileId] = useState<string | null>(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [offset, setOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [ridesData, setRidesData] = useState<Ride[]>([]);

  useEffect(() => {
    const getProfileId = async () => {
      try {
        const id = await AsyncStorage.getItem('profileId');
        setProfileId(id);
      } catch (error) {
        // Handle error silently
      }
    };
    getProfileId();
  }, []);

  const { data, loading, error, fetchMore } = useQuery<RideHistoryData>(GET_RIDE_HISTORY, {
    variables: {
      profileId,
      limit: ITEMS_PER_PAGE,
      offset,
    },
    skip: !profileId,
  });

  useEffect(() => {
    if (data?.free_rides) {
      if (offset === 0) {
        setRidesData(data.free_rides);
      }
    }
  }, [data, offset]);

  const loadMore = useCallback(async () => {
    if (loading || isLoadingMore || !hasMore || !profileId) return;

    try {
      setIsLoadingMore(true);
      const newOffset = offset + ITEMS_PER_PAGE;

      const { data: newData } = await fetchMore<RideHistoryData>({
        variables: {
          profileId,
          offset: newOffset,
          limit: ITEMS_PER_PAGE,
        },
      });

      setOffset(newOffset);
      setRidesData(prevRides => [...prevRides, ...(newData?.free_rides || [])]);
      setHasMore(newData?.free_rides?.length === ITEMS_PER_PAGE);
    } catch (error) {
      console.error('Error loading more rides:', error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [loading, isLoadingMore, hasMore, profileId, offset, fetchMore]);

  const renderItem = useCallback(
    ({ item: ride }: { item: Ride }) => (
      <RideItem
        key={ride.id}
        ride={ride}
        theme={theme}
        t={t}
        onCallPress={handleCall}
        // onPress={(rideId) => router.push(`/ride/${rideId}`)}
      />
    ),
    [theme, t]
  );

  const keyExtractor = useCallback((item: Ride) => item.id, []);

  const renderEmpty = useCallback(
    () => (
      <View style={styles.emptyState}>
        <MaterialCommunityIcons name="car-off" size={64} color={theme.colors.primary} />
        <Text
          variant="titleMedium"
          style={[styles.emptyStateText, { color: theme.colors.onBackground }]}
        >
          {t('settings.rideHistory.noRides')}
        </Text>
      </View>
    ),
    [theme.colors, t]
  );

  const renderFooter = useCallback(() => {
    if (loading || isLoadingMore) {
      return (
        <ActivityIndicator size="small" color={theme.colors.primary} style={styles.footerLoader} />
      );
    }

    if (!loading && ridesData.length > 0 && !hasMore) {
      return (
        <Text
          variant="bodySmall"
          style={[styles.endOfListText, { color: theme.colors.onSurfaceVariant }]}
        >
          {t('common.endOfList')}
        </Text>
      );
    }

    return null;
  }, [loading, isLoadingMore, ridesData.length, hasMore, theme.colors, t]);

  const renderHeader = useMemo(
    () => (
      <View style={styles.header}>
        <Surface
          style={[styles.headerCard, { backgroundColor: theme.colors.surface }]}
          elevation={4}
        >
          <View style={styles.headerLeft}>
            <IconButton
              icon="arrow-left"
              size={24}
              onPress={() => router.back()}
              iconColor={theme.colors.onSurface}
            />
            <Text variant="headlineSmall" style={{ color: theme.colors.onSurface }}>
              {t('settings.rideHistory.title')}
            </Text>
          </View>
        </Surface>
      </View>
    ),
    [theme.colors, t, data, ridesData]
  );

  if (loading && !ridesData.length) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Text>{t('settings.rideHistory.loadingError')}</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderHeader}
      <FlashList
        data={ridesData}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        estimatedItemSize={ESTIMATED_ITEM_SIZE}
        ListEmptyComponent={renderEmpty}
        ListFooterComponent={renderFooter}
        onEndReached={loadMore}
        onEndReachedThreshold={0.5}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 8,
  },
  header: {
    paddingTop: 8,
    paddingHorizontal: 8,
  },
  headerCard: {
    margin: 8,
    padding: 8,
    borderRadius: 12,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyStateText: {
    marginTop: 16,
    textAlign: 'center',
  },
  footerLoader: {
    padding: 16,
  },
  endOfListText: {
    padding: 16,
    textAlign: 'center',
  },
});
