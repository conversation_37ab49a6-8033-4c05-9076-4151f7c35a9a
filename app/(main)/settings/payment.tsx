import React from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { List, Text, Button, Surface, useTheme, IconButton, Divider } from 'react-native-paper';
import { router } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';

export default function PaymentSettingsScreen() {
  const theme = useTheme();

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <IconButton
          icon="arrow-left"
          size={24}
          onPress={() => router.back()}
        />
        <Text variant="titleLarge" style={styles.headerTitle}>Payment Information</Text>
      </View>

      <Surface style={styles.section} elevation={1}>
        <View style={styles.sectionHeader}>
          <MaterialCommunityIcons name="credit-card" size={24} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>Payment Methods</Text>
        </View>
        
        <List.Item
          title="**** **** **** 1234"
          description="Expires 12/25"
          left={props => <List.Icon {...props} icon="credit-card" />}
          right={props => <Text>Default</Text>}
        />
        <Divider />
        <List.Item
          title="Add Payment Method"
          left={props => <List.Icon {...props} icon="plus-circle" />}
          onPress={() => {}}
        />
      </Surface>

      <Surface style={styles.section} elevation={1}>
        <View style={styles.sectionHeader}>
          <MaterialCommunityIcons name="bank" size={24} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>Bank Account</Text>
        </View>
        
        <List.Item
          title="**** 5678"
          description="Savings Account"
          left={props => <List.Icon {...props} icon="bank" />}
        />
        <Divider />
        <List.Item
          title="Add Bank Account"
          left={props => <List.Icon {...props} icon="plus-circle" />}
          onPress={() => {}}
        />
      </Surface>

      <Surface style={styles.section} elevation={1}>
        <View style={styles.sectionHeader}>
          <MaterialCommunityIcons name="history" size={24} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>Payment History</Text>
        </View>
        
        <List.Item
          title="Last Week's Earnings"
          description="$450.00"
          left={props => <List.Icon {...props} icon="cash" />}
        />
        <List.Item
          title="This Month's Earnings"
          description="$1,850.00"
          left={props => <List.Icon {...props} icon="cash-multiple" />}
        />
        <Divider />
        <List.Item
          title="View Full History"
          left={props => <List.Icon {...props} icon="chevron-right" />}
          onPress={() => {}}
        />
      </Surface>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
  },
  headerTitle: {
    marginLeft: 8,
    fontWeight: '600',
  },
  section: {
    margin: 16,
    borderRadius: 12,
    backgroundColor: '#fff',
    overflow: 'hidden',
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sectionTitle: {
    marginLeft: 8,
    fontWeight: '600',
  },
});
