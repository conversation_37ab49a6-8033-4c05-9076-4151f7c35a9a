import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Platform, Linking, FlatList } from 'react-native';
import {
  Text,
  Surface,
  IconButton,
  ActivityIndicator,
  List,
  Avatar,
  Chip,
  Divider,
  Button,
} from 'react-native-paper';
import { router } from 'expo-router';
import { useUserId } from '@nhost/react';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../../../src/theme/ThemeContext';
import { useQuery } from '@apollo/client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { formatDistanceToNow } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { GET_FREE_RIDE_REQUESTS } from 'app/graphql/ride';

const ITEMS_PER_PAGE = 10;

const getStatusColor = (status: string, theme: any) => {
  switch (status?.toLowerCase()) {
    case 'completed':
      return theme.colors.primary;
    case 'cancelled':
      return theme.colors.error;
    default:
      return theme.colors.secondary;
  }
};

export default function RideHistoryScreen() {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const userId = useUserId();

  const [state, setState] = useState({
    profileId: null as string | null,
  });
  const [rides, setRides] = useState<any[]>([]);
  const [offset, setOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const { data, loading, error, fetchMore } = useQuery(GET_FREE_RIDE_REQUESTS, {
    skip: !state.profileId,
    variables: {
      profileId: state.profileId,
      offset: 0,
      limit: ITEMS_PER_PAGE,
    },
    // fetchPolicy: 'network-only',
  });

  useEffect(() => {
    const fetchProfileId = async () => {
      try {
        const id = await AsyncStorage.getItem('profileId');
        setState(prevState => ({ ...prevState, profileId: id }));
      } catch (error) {
        console.error('Error fetching profile ID:', error);
      }
    };
    fetchProfileId();
  }, []);

  useEffect(() => {
    if (data?.free_rides) {
      setRides(data.free_rides);
      setHasMore(data.free_rides.length === ITEMS_PER_PAGE);
    }
  }, [data]);

  const loadMore = async () => {
    if (loading || !hasMore || !state.profileId) return;

    try {
      const { data: newData } = await fetchMore({
        variables: {
          profileId: state.profileId,
          offset: offset + ITEMS_PER_PAGE,
          limit: ITEMS_PER_PAGE,
        },
        updateQuery: (prev, { fetchMoreResult }) => {
          if (!fetchMoreResult) return prev;
          return {
            free_rides: [...prev.free_rides, ...fetchMoreResult.free_rides],
          };
        },
      });

      if (newData?.free_rides) {
        setOffset(prev => prev + ITEMS_PER_PAGE);
        setHasMore(newData.free_rides.length === ITEMS_PER_PAGE);
      }
    } catch (error) {
      console.error('Error loading more rides:', error);
    }
  };

  const handleCall = (phoneNumber: string) => {
    const url = Platform.select({
      ios: `tel:${phoneNumber}`,
      android: `tel:${phoneNumber}`,
    });
    Linking.openURL(url as string);
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <Surface style={[styles.headerCard, { backgroundColor: theme.colors.surface }]} elevation={4}>
        <View style={styles.headerLeft}>
          <IconButton
            icon="arrow-left"
            size={24}
            onPress={() => router.back()}
            iconColor={theme.colors.onSurface}
          />
          <Text variant="headlineSmall" style={{ color: theme.colors.onSurface }}>
            {t('rideHistory.title')}
          </Text>
        </View>
      </Surface>
    </View>
  );

  const renderItem = ({ item: ride }: { item: any }) => (
    <Surface
      key={ride?.id}
      style={[styles.rideCard, { backgroundColor: theme.colors.surface }]}
      elevation={2}
    >
      <View style={styles.rideHeader}>
        <View style={styles.statusContainer}>
          <Chip
            mode="flat"
            style={[styles.statusChip, { backgroundColor: getStatusColor(ride.status, theme) }]}
            textStyle={{ color: theme.colors.surface, fontSize: 12 }}
          >
            {ride.status || 'Unknown'}
          </Chip>
          <Text
            variant="labelSmall"
            style={[styles.timeAgo, { color: theme.colors.onSurfaceVariant }]}
          >
            {formatDistanceToNow(new Date(ride.updated_at), { addSuffix: true })}
          </Text>
        </View>
        {ride.driver?.phone_number && (
          <IconButton
            icon="phone"
            size={20}
            style={styles.phoneButton}
            onPress={() => handleCall(ride.driver.phone_number)}
            iconColor={theme.colors.primary}
          />
        )}
      </View>

      <View style={styles.locationContainer}>
        <View style={styles.locationItem}>
          <MaterialCommunityIcons name="map-marker" size={20} color={theme.colors.primary} />
          <View style={styles.locationText}>
            <Text variant="labelSmall" style={{ color: theme.colors.onSurfaceVariant }}>
              {t('rideHistory.pickup')}
            </Text>
            <Text variant="bodySmall" numberOfLines={2} style={{ color: theme.colors.onSurface }}>
              {ride.pickup_address || 'N/A'}
            </Text>
          </View>
        </View>

        <View style={styles.locationDivider}>
          <MaterialCommunityIcons
            name="dots-vertical"
            size={16}
            color={theme.colors.onSurfaceVariant}
          />
        </View>

        <View style={styles.locationItem}>
          <MaterialCommunityIcons name="map-marker-radius" size={20} color={theme.colors.error} />
          <View style={styles.locationText}>
            <Text variant="labelSmall" style={{ color: theme.colors.onSurfaceVariant }}>
              {t('rideHistory.dropoff')}
            </Text>
            <Text variant="bodySmall" numberOfLines={2} style={{ color: theme.colors.onSurface }}>
              {ride.dropoff_address || 'N/A'}
            </Text>
          </View>
        </View>
      </View>

      <Divider style={[styles.divider, { backgroundColor: theme.colors.outlineVariant }]} />

      {ride.driver && (
        <View style={styles.driverInfo}>
          <Avatar.Icon
            icon="car"
            size={36}
            style={{ backgroundColor: theme.colors.onPrimary }}
            color={theme.colors.primary}
          />
          <View style={styles.driverDetails}>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
              {ride.driver.name}
            </Text>
            {ride.driver.vehicles?.[0] && (
              <View style={styles.vehicleInfo}>
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                  {ride.driver.vehicles[0].make} {ride.driver.vehicles[0].model}
                </Text>
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                  • {ride.driver.vehicles[0].color} • {ride.driver.vehicles[0].license_plate}
                </Text>
              </View>
            )}
          </View>
        </View>
      )}

      {ride.status?.toLowerCase() === 'cancelled' && ride.cancel_reason && (
        <View style={styles.cancelReason}>
          <Text variant="bodySmall" style={{ color: theme.colors.error }}>
            Cancelled: {ride.cancel_reason}
          </Text>
        </View>
      )}
    </Surface>
  );

  const renderEmpty = () => (
    <View style={styles.emptyState}>
      <MaterialCommunityIcons name="car-off" size={64} color={theme.colors.primary} />
      <Text
        variant="titleMedium"
        style={[styles.emptyStateText, { color: theme.colors.onBackground }]}
      >
        {t('rideHistory.noRides')}
      </Text>
    </View>
  );

  if (loading && !rides.length) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Text>Error loading ride history</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FlatList
        data={rides}
        renderItem={renderItem}
        keyExtractor={item => item?.id}
        contentContainerStyle={styles.scrollContent}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmpty}
        onEndReached={loadMore}
        onEndReachedThreshold={0.5}
        stickyHeaderIndices={[0]}
        ListFooterComponent={
          loading && rides.length > 0 ? (
            <ActivityIndicator
              size="small"
              color={theme.colors.primary}
              style={styles.footerLoader}
            />
          ) : (
            <Button
              mode="contained"
              onPress={loadMore}
              loading={loading}
              style={styles.loadMoreButton}
            >
              {t('rideHistory.loadMore')}
            </Button>
          )
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 12,
  },
  header: {
    marginBottom: 12,
  },
  headerCard: {
    padding: 8,
    borderRadius: 12,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    marginLeft: 8,
    fontWeight: '600',
  },
  content: {
    // padding: 16,
  },
  rideCard: {
    marginBottom: 12,
    borderRadius: 12,
    // overflow: 'hidden',
    padding: 12,
  },
  rideHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusChip: {
    // height: 24,
    borderRadius: 12,
  },
  timeAgo: {
    fontSize: 11,
  },
  phoneButton: {
    margin: 0,
  },
  locationContainer: {
    marginVertical: 4,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 2,
  },
  locationText: {
    marginLeft: 8,
    flex: 1,
  },
  locationDivider: {
    alignItems: 'center',
    marginLeft: 10,
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  driverDetails: {
    marginLeft: 8,
    flex: 1,
  },
  vehicleInfo: {
    flexDirection: 'column',
    gap: 2,
  },
  divider: {
    marginVertical: 8,
  },
  cancelReason: {
    marginTop: 8,
    padding: 6,
    backgroundColor: '#FFE5E5',
    borderRadius: 6,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
  },
  emptyStateText: {
    marginTop: 16,
    textAlign: 'center',
  },
  footerLoader: {
    paddingVertical: 16,
  },
  loadMoreButton: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
});
