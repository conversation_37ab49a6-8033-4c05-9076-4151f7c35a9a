import React from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { List, Text, Button, Surface, useTheme, IconButton, Chip } from 'react-native-paper';
import { router } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';

export default function DocumentsScreen() {
  const theme = useTheme();

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <IconButton
          icon="arrow-left"
          size={24}
          onPress={() => router.back()}
        />
        <Text variant="titleLarge" style={styles.headerTitle}>Documents</Text>
      </View>

      <Surface style={styles.section} elevation={1}>
        <View style={styles.sectionHeader}>
          <MaterialCommunityIcons name="card-account-details" size={24} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>Driver's License</Text>
        </View>
        
        <List.Item
          title="Driver's License"
          description="Expires on 12/2025"
          left={props => <List.Icon {...props} icon="card-account-details" />}
          right={() => (
            <Chip mode="outlined" style={styles.statusChip} textStyle={{ color: '#4CAF50' }}>
              Active
            </Chip>
          )}
        />
        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={() => {}}
            style={styles.button}
          >
            Update License
          </Button>
        </View>
      </Surface>

      <Surface style={styles.section} elevation={1}>
        <View style={styles.sectionHeader}>
          <MaterialCommunityIcons name="shield-check" size={24} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>Insurance Documents</Text>
        </View>
        
        <List.Item
          title="Vehicle Insurance"
          description="Expires on 06/2024"
          left={props => <List.Icon {...props} icon="car" />}
          right={() => (
            <Chip mode="outlined" style={styles.statusChip} textStyle={{ color: '#4CAF50' }}>
              Active
            </Chip>
          )}
        />
        <List.Item
          title="Liability Insurance"
          description="Expires on 06/2024"
          left={props => <List.Icon {...props} icon="shield" />}
          right={() => (
            <Chip mode="outlined" style={styles.statusChip} textStyle={{ color: '#4CAF50' }}>
              Active
            </Chip>
          )}
        />
        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={() => {}}
            style={styles.button}
          >
            Update Insurance
          </Button>
        </View>
      </Surface>

      <Surface style={styles.section} elevation={1}>
        <View style={styles.sectionHeader}>
          <MaterialCommunityIcons name="file-certificate" size={24} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>Additional Documents</Text>
        </View>
        
        <List.Item
          title="Background Check"
          description="Completed on 01/2024"
          left={props => <List.Icon {...props} icon="check-circle" />}
          right={() => (
            <Chip mode="outlined" style={styles.statusChip} textStyle={{ color: '#4CAF50' }}>
              Verified
            </Chip>
          )}
        />
        <List.Item
          title="Vehicle Registration"
          description="Expires on 12/2024"
          left={props => <List.Icon {...props} icon="car-clock" />}
          right={() => (
            <Chip mode="outlined" style={styles.statusChip} textStyle={{ color: '#4CAF50' }}>
              Active
            </Chip>
          )}
        />
        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={() => {}}
            style={styles.button}
          >
            Upload Document
          </Button>
        </View>
      </Surface>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
  },
  headerTitle: {
    marginLeft: 8,
    fontWeight: '600',
  },
  section: {
    margin: 16,
    borderRadius: 12,
    backgroundColor: '#fff',
    overflow: 'hidden',
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sectionTitle: {
    marginLeft: 8,
    fontWeight: '600',
  },
  buttonContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  button: {
    borderRadius: 8,
  },
  statusChip: {
    borderColor: '#4CAF50',
  },
});
