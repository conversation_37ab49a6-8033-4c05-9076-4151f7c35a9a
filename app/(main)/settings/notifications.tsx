import React, { useCallback } from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { Text, Surface, IconButton, List, Switch, ActivityIndicator } from 'react-native-paper';
import { router } from 'expo-router';
import { useTheme } from '../../../src/theme/ThemeContext';
import { useTranslation } from 'react-i18next';
import { useNotifications } from 'app/contexts/NotificationContext';

export default function NotificationsScreen() {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const {
    isNotificationsEnabled,
    enableNotifications,
    disableNotifications,
    // sendLocalNotification,
  } = useNotifications();

  const [isLoading, setIsLoading] = React.useState(false);

  const handleToggleNotifications = async () => {
    setIsLoading(true);
    if (isNotificationsEnabled) {
      await disableNotifications();
    } else {
      await enableNotifications();
    }
    setIsLoading(false);
  };

  // const handleTestNotification = async () => {
  //   if (isTestingNotification) return; // Prevent multiple rapid calls

  //   try {
  //     setIsTestingNotification(true);

  //     await sendLocalNotification(
  //       'Test Notification',
  //       'This is a test notification from TotoLoco!'
  //     );
  //   } finally {
  //     // Add a small delay before allowing another notification
  //     setTimeout(() => {
  //       setIsTestingNotification(false);
  //     }, 1000); // 1 second delay
  //   }
  // };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      contentContainerStyle={styles.scrollContent}
      stickyHeaderIndices={[0]}
    >
      <View style={styles.header}>
        <Surface
          style={[styles.headerCard, { backgroundColor: theme.colors.surface }]}
          elevation={4}
        >
          <View style={styles.headerLeft}>
            <IconButton
              icon="arrow-left"
              size={24}
              iconColor={theme.colors.onSurface}
              onPress={() => router.back()}
            />
            <Text
              variant="titleLarge"
              style={[styles.headerTitle, { color: theme.colors.onSurface }]}
            >
              {t('settings.notifications')}
            </Text>
          </View>
        </Surface>
      </View>

      <Surface
        style={[styles.settingsCard, { backgroundColor: theme.colors.surface }]}
        elevation={2}
      >
        <List.Section>
          <List.Item
            title={t('settings.pushNotifications')}
            description={t('settings.pushNotificationsDesc')}
            left={props => <List.Icon {...props} icon="bell" color={theme.colors.primary} />}
            right={() =>
              isLoading ? (
                <ActivityIndicator />
              ) : (
                <Switch
                  value={isNotificationsEnabled}
                  onValueChange={handleToggleNotifications}
                  color={theme.colors.primary}
                />
              )
            }
            titleStyle={{ color: theme.colors.onSurface }}
            descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
          />
          {/* {isNotificationsEnabled && (
            <List.Item
              title="Test Notification"
              description="Send a test notification"
              left={props => <List.Icon {...props} icon="bell-ring" color={theme.colors.primary} />}
              onPress={handleTestNotification}
              titleStyle={{ color: theme.colors.onSurface }}
              descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
            />
          )} */}
        </List.Section>
      </Surface>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    gap: 16,
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  headerCard: {
    borderRadius: 16,
    padding: 8,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerTitle: {
    fontWeight: 'bold',
  },
  settingsCard: {
    borderRadius: 16,
    // overflow: 'hidden',
  },
});
