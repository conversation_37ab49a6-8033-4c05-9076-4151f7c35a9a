import React, { useState, useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import { Text, Avatar, IconButton, Surface } from 'react-native-paper';
import { router } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useSignOut, useUserDefaultRole, useUserId } from '@nhost/react';

import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../../src/theme/ThemeContext';
import { useQuery, gql } from '@apollo/client';
import MapView from 'app/components/MapView';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import AnimatedLoadingScreen from 'app/components/AnimatedLoadingScreen';
import { Image } from 'react-native';
import { TouchableOpacity } from 'react-native';
import { GET_USER_PROFILE } from 'app/graphql/passenger';
import LocationSelector from 'app/components/LocationSelector';

export default function HomeScreen() {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const [isValidating, setIsValidating] = useState(true);

  const { signOut } = useSignOut();
  const userId = useUserId();
  const defaultRole = useUserDefaultRole();
  const {
    data: existingProfileData,
    loading: profileLoading,
    error: profileError,
  } = useQuery(GET_USER_PROFILE, {
    variables: { userId },
    fetchPolicy: 'network-only',
    skip: !userId,
  });

  const profileData = existingProfileData?.user_profiles[0];

  useEffect(() => {
    const checkprofileStatus = async () => {
      try {
        if (defaultRole !== 'user') {
          Alert.alert(t('dashboard.accessDenied'), t('dashboard.driverMessage'), [
            {
              text: t('dashboard.okay'),
              onPress: async () => {
                await AsyncStorage.clear();
                await signOut();
              },
            },
          ]);

          return;
        }

        // If we have profile data, update storage
        if (profileData?.id) {
          await AsyncStorage.setItem('profileId', profileData?.id);
        }

        // Only check validation conditions if we have loaded the data
        if (!profileLoading && profileData) {
          const needsOnboarding = !profileData?.id;

          if (needsOnboarding) {
            router.replace('/(main)/onboarding-user');
            return;
          }
        } else {
          router.replace('/(main)/onboarding-user');
          return;
        }

        setIsValidating(false);
      } catch (error) {
        console.error('Error checking profile status:', error);
        setIsValidating(false);
      }
    };

    // Only run the check when we have finished loading and have a userId
    if (!profileLoading && userId) {
      checkprofileStatus();
    }
  }, [userId, defaultRole, profileData, profileLoading]);

  if (profileLoading || isValidating) {
    return <AnimatedLoadingScreen />;
  }

  if (profileError) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: theme.colors.background }]}>
        <MaterialCommunityIcons name="alert-circle" size={48} color={theme.colors.error} />
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          Something went wrong, please report this issue to developer
        </Text>
      </View>
    );
  }

  return (
    <View
      style={[styles.container, { backgroundColor: theme.colors.background }]}
    // contentContainerStyle={styles.scrollContent}
    >
      <View style={styles.header}>
        <Surface
          style={[styles.headerCard, { backgroundColor: theme.colors.surface }]}
          elevation={4}
        >
          <TouchableOpacity onPress={() => router.push('/settings/profile')}>
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                width: 48,
                height: 48,
              }}
            >
              <Image
                source={require('../../assets/images/bolaoo-logo.png')}
                style={{ width: 60, height: 60 }}
                resizeMode="contain"
              />
            </View>
          </TouchableOpacity>

          {/* <Avatar.Icon
            size={48}
            icon="account"
            style={{ backgroundColor: 'transparent' }}
            color={theme.colors.primary}
          /> */}
          <View style={styles.headerText}>
            <View style={styles.nameContainer}>
              <Text
                variant="bodyMedium"
                style={[styles.welcomeText, { color: theme.colors.onSurface }]}
                numberOfLines={1}
              >
                {t('dashboard.greetings', { name: profileData?.full_name })}
              </Text>
              <Text
                variant="bodyMedium"
                style={[styles.headerSubtitle, { color: theme.colors.onSurface }]}
                numberOfLines={1}
              >
                {t('dashboard.readyToRide')}
              </Text>
            </View>
          </View>

          <IconButton
            icon="cog"
            size={32}
            iconColor={theme.colors.onSurface}
            style={styles.iconButton}
            onPress={() => router.push('/(main)/settings')}
            accessibilityLabel={t('dashboard.settings')}
          />
        </Surface>
      </View>

      <View style={styles.mapContainer}>
        <MapView latitude={0} longitude={0} />
      </View>

      <LocationSelector />

      <View style={[styles.footer]}>
        <Text style={[styles.footerText, { color: theme.colors.onSurfaceVariant }]}>
          Made in Jhapa with <Text style={{ color: theme.colors.error }}>♥</Text>
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginVertical: 16,
  },
  header: {
    // marginBottom: 16,
  },
  headerCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 16,
  },
  headerText: {
    flex: 1,
    // marginLeft: 16,
  },
  nameContainer: {
    // flexDirection: 'row',
    // alignItems: 'center',
    gap: 4,
  },
  welcomeText: {
    fontWeight: 'bold',
  },
  headerSubtitle: {
    opacity: 0.7,
    fontSize: 12,
  },
  verificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  verificationIcon: {
    marginRight: 4,
  },
  verificationText: {
    fontSize: 12,
    fontWeight: '500',
  },
  iconButton: {
    margin: 0,
  },
  statusContainer: {
    marginBottom: 16,
  },
  statusCard: {
    padding: 16,
    borderRadius: 12,
  },
  statusContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statusLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusText: {
    fontWeight: '500',
  },
  trackingStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    gap: 8,
  },
  pulseContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pulse: {
    position: 'absolute',
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  trackingText: {
    fontSize: 14,
    opacity: 0.7,
  },
  mapContainer: {
    // height: 500,
    flex: 1,
    marginTop: 8,
    borderRadius: 12,
    // overflow: 'hidden',
  },
  statsContainer: {
    marginBottom: 16,
  },
  statsCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
  },
  stat: {
    flex: 1,
    alignItems: 'center',
  },
  statDivider: {
    width: 1,
    opacity: 0.1,
    marginHorizontal: 16,
  },
  statValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  statLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  placeholderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  placeholderTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  placeholderText: {
    fontSize: 14,
    opacity: 0.7,
  },
  scrollView: {
    flex: 1,
  },
  footer: {
    // paddingBottom: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  footerText: {
    fontSize: 12,
    opacity: 0.8,
  },
  versionText: {
    fontSize: 11,
    opacity: 0.6,
    marginTop: 4,
  },
});
