import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Surface } from 'react-native-paper';
import DashboardMap from '../components/DashboardMap';
import LocationSelector from '../components/LocationSelector';
import { useTheme } from '../../src/theme/ThemeContext';

export default function DashboardMapScreen() {
  const { theme } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.mapContainer}>
        <DashboardMap />
      </View>
      
      <Surface style={[styles.overlay, { backgroundColor: theme.colors.surface }]} elevation={4}>
        <LocationSelector />
      </Surface>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mapContainer: {
    flex: 1,
  },
  overlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
});
