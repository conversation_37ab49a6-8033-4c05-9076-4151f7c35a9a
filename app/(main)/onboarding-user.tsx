import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  View,
  ScrollView,
  Image,
  Platform,
  Modal,
  TouchableOpacity,
} from 'react-native';
import {
  TextInput,
  HelperText,
  Surface,
  Text,
  Button,
  SegmentedButtons,
  TouchableRipple,
} from 'react-native-paper';
import { router } from 'expo-router';
import { useUserDisplayName, useUserId } from '@nhost/react';
import { useMutation, useQuery } from '@apollo/client';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';
import { CREATE_USER_PROFILE, GET_USER_PROFILE, UPDATE_USER_PROFILE } from 'app/graphql/passenger';
import { useTheme } from '../../src/theme/ThemeContext';
import { COLORS } from '@/constants';
import AnimatedLoadingScreen from 'app/components/AnimatedLoadingScreen';
import useLocationServices from 'app/hooks/useLocationServices';

type BloodGroup = 'A+' | 'A-' | 'B+' | 'B-' | 'AB+' | 'AB-' | 'O+' | 'O-' | 'unknown';

type ValidationErrors = {
  fullName?: string;
  phone?: string;
  day?: string;
  month?: string;
  year?: string;
  gender?: string;
  bloodGroup?: string;
};

type Gender = 'male' | 'female' | 'other';

export default function OnboardingUserScreen() {
  const displayName = useUserDisplayName();
  const userId = useUserId();
  const {
    data: existingProfileData,
    loading: existingProfileLoading,
    error: existingProfileError,
  } = useQuery(GET_USER_PROFILE, {
    variables: { userId },
    fetchPolicy: 'network-only',
  });

  const existingProfile = existingProfileData?.user_profiles[0];

  const { theme } = useTheme();
  const { currentLocation, startForegroundTracking } = useLocationServices();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});

  const monthInputRef = useRef<any>(null);
  const yearInputRef = useRef<any>(null);

  // Profile Information
  const [fullName, setFullName] = useState(displayName || '');
  const [phone, setPhone] = useState('');
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const [day, setDay] = useState<string>('');
  const [month, setMonth] = useState<string>('');
  const [year, setYear] = useState<string>('');
  const [gender, setGender] = useState<Gender>('male');
  const [bloodGroup, setBloodGroup] = useState<BloodGroup>('unknown');
  const [showBloodGroupDropdown, setShowBloodGroupDropdown] = useState(false);
  const [bloodDonationConsent, setBloodDonationConsent] = useState(false);

  const [createUserProfile, { error: createUserProfileError }] = useMutation(CREATE_USER_PROFILE, {
    onCompleted: () => {
      router.replace('/(main)');
    },
  });

  const [updateUserProfile] = useMutation(UPDATE_USER_PROFILE, {
    onCompleted: () => {
      router.replace('/(main)');
    },
  });

  useEffect(() => {
    const setupTracking = async () => {
      // if(!currentLocation){
      await startForegroundTracking();
      // }
    };

    setupTracking();
  }, [startForegroundTracking]);

  useEffect(() => {
    if (existingProfile) {
      setFullName(existingProfile.full_name || '');
      setPhone(existingProfile.phone || '');
      setAvatarUrl(existingProfile.avatar_url || null);
      const dob = new Date(existingProfile.dob);
      setDay(dob.getDate().toString());
      setMonth((dob.getMonth() + 1).toString());
      setYear(dob.getFullYear().toString());
      setGender(existingProfile.gender || 'male');
      setBloodGroup(existingProfile.blood_group || 'unknown');
      setBloodDonationConsent(existingProfile.blood_donation_consent || false);
    }
  }, [existingProfile]);

  const validateFullName = (value: string) => {
    if (!value.trim()) return 'Full name is required';
    if (value.length < 2) return 'Full name must be at least 2 characters';
    return '';
  };

  const validatePhone = (value: string) => {
    if (!value.trim()) return 'Phone number is required';

    // Remove any spaces, dashes, or plus signs
    const cleanNumber = value.replace(/[\s-+]/g, '');

    // Remove country code if present
    const numberWithoutCountry = cleanNumber.startsWith('977')
      ? cleanNumber.substring(3)
      : cleanNumber;

    // Check if it's a valid Nepal mobile number
    if (numberWithoutCountry.length !== 10) {
      return 'Phone number must be 10 digits long';
    }

    if (!numberWithoutCountry.startsWith('9')) {
      return 'Phone number must start with 9';
    }

    if (!/^\d+$/.test(numberWithoutCountry)) {
      return 'Phone number can only contain digits';
    }

    return '';
  };

  const validateDay = (value: string) => {
    const dayNum = parseInt(value);
    if (!value) return 'Required';
    if (isNaN(dayNum) || dayNum < 1 || dayNum > 31) return 'Invalid day';
    return '';
  };

  const validateMonth = (value: string) => {
    const monthNum = parseInt(value);
    if (!value) return 'Required';
    if (isNaN(monthNum) || monthNum < 1 || monthNum > 12) return 'Invalid month';
    return '';
  };

  const validateYear = (value: string) => {
    const yearNum = parseInt(value);
    const currentYear = new Date().getFullYear();
    if (!value) return 'Required';
    if (isNaN(yearNum) || yearNum < 1950 || yearNum > currentYear) return 'Invalid year';
    return '';
  };

  const validateBloodGroup = (value: string) => {
    if (!value || value === 'unknown') return '';
    return '';
  };

  const handleSubmit = async () => {
    const errors: ValidationErrors = {};
    const nameError = validateFullName(fullName);
    const phoneError = validatePhone(phone);
    const dayError = validateDay(day);
    const monthError = validateMonth(month);
    const yearError = validateYear(year);
    const bloodGroupError = validateBloodGroup(bloodGroup);

    if (nameError) errors.fullName = nameError;
    if (phoneError) errors.phone = phoneError;
    if (dayError) errors.day = dayError;
    if (monthError) errors.month = monthError;
    if (yearError) errors.year = yearError;
    if (bloodGroupError) errors.bloodGroup = bloodGroupError;

    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      return;
    }

    // Create Date object from day, month, year
    const dobDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

    setIsSubmitting(true);
    try {
      const variables = {
        userId,
        fullName,
        phone,
        avatarUrl,
        dob: dobDate.toISOString().split('T')[0],
        gender,
        bloodGroup,
        bloodDonationConsent,
        metadata: {},
        location: {
          type: 'Point',
          coordinates: [
            Number(currentLocation?.coords?.longitude),
            Number(currentLocation?.coords?.latitude),
          ],
        },
      };

      if (existingProfile) {
        await updateUserProfile({
          variables: {
            profileId: existingProfile?.id,
            ...variables,
          },
        });
      } else {
        await createUserProfile({
          variables,
        });
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const setPhoneAndClearError = (value: string) => {
    setPhone(value);
    setValidationErrors(prev => ({ ...prev, phone: undefined }));
  };

  const renderUserProfileForm = () => {
    return (
      <Animated.View entering={FadeInUp.duration(1000).springify()} style={styles.formContainer}>
        <Surface style={styles.headerContainer}>
          <LinearGradient
            colors={[theme.colors.primary, theme.colors.secondary]}
            style={styles.headerGradient}
          >
            <Animated.View entering={FadeInDown.delay(300)} style={styles.headerContent}>
              <Image
                source={require('../../assets/images/splash-icon.png')}
                style={styles.logo}
                resizeMode="contain"
              />
              <Text style={styles.headerTitle}>Welcome!</Text>
              <Text style={styles.headerSubtitle}>Let's get you started with your profile</Text>
            </Animated.View>
          </LinearGradient>
        </Surface>

        <View style={styles.formSection}>
          <Animated.View entering={FadeInUp.delay(400)}>
            <TextInput
              label="Full Name"
              value={fullName}
              onChangeText={setFullName}
              mode="outlined"
              error={!!validationErrors.fullName}
              style={[styles.input, { backgroundColor: theme.colors.surface }]}
              left={<TextInput.Icon icon="account" />}
            />
            <HelperText type="error" visible={!!validationErrors.fullName}>
              {validationErrors.fullName}
            </HelperText>
          </Animated.View>

          <Animated.View entering={FadeInUp.delay(500)}>
            <TextInput
              label="Phone Number"
              value={phone}
              onChangeText={setPhoneAndClearError}
              mode="outlined"
              error={!!validationErrors.phone}
              style={[styles.input, { backgroundColor: theme.colors.surface }]}
              keyboardType="phone-pad"
              left={<TextInput.Affix text="+977" />}
            />
            <HelperText type="error" visible={!!validationErrors.phone}>
              {validationErrors.phone}
            </HelperText>
          </Animated.View>

          <Animated.View entering={FadeInUp.delay(700)} style={{ marginBottom: 8 }}>
            {/* <Text style={[styles.sectionLabel, { color: theme.colors.onSurface }]}>Gender</Text> */}
            <SegmentedButtons
              value={gender}
              onValueChange={setGender as (value: string) => void}
              buttons={[
                {
                  value: 'male',
                  label: 'Male',
                  icon: 'gender-male',
                  style: styles.segmentButton,
                  checkedColor: theme.colors.onPrimary,
                },
                {
                  value: 'female',
                  label: 'Female',
                  icon: 'gender-female',
                  style: styles.segmentButton,
                  checkedColor: theme.colors.onPrimary,
                },
                {
                  value: 'other',
                  label: 'Other',
                  icon: 'gender-non-binary',
                  style: styles.segmentButton,
                  checkedColor: theme.colors.onPrimary,
                },
              ]}
              style={styles.genderButtons}
            />
          </Animated.View>

          <Animated.View entering={FadeInUp.delay(600)}>
            <Text style={[styles.sectionLabel, { color: theme.colors.onSurface }]}>
              Date of Birth (AD)
            </Text>
            <View style={styles.dobContainer}>
              <View style={styles.dobInputContainer}>
                <TextInput
                  label="Day"
                  value={day}
                  onChangeText={text => {
                    const filtered = text.replace(/[^0-9]/g, '');
                    if (filtered.length <= 2) {
                      setDay(filtered);
                      if (filtered.length === 2) {
                        // Auto-focus to month input when 2 digits are entered
                        monthInputRef?.current?.focus();
                      }
                    }
                  }}
                  mode="outlined"
                  keyboardType="number-pad"
                  maxLength={2}
                  style={[styles.dobInput, { backgroundColor: theme.colors.surface }]}
                  error={!!validationErrors.day}
                  placeholder="DD"
                />
                <HelperText type="error" visible={!!validationErrors.day}>
                  {validationErrors.day}
                </HelperText>
              </View>

              <View style={styles.dobSeparator}>
                <Text style={{ color: theme.colors.onSurface }}>/</Text>
              </View>

              <View style={styles.dobInputContainer}>
                <TextInput
                  ref={monthInputRef}
                  label="Month"
                  value={month}
                  onChangeText={text => {
                    const filtered = text.replace(/[^0-9]/g, '');
                    if (filtered.length <= 2) {
                      setMonth(filtered);
                      if (filtered.length === 2) {
                        // Auto-focus to year input when 2 digits are entered
                        yearInputRef.current?.focus?.();
                      }
                    }
                  }}
                  mode="outlined"
                  keyboardType="number-pad"
                  maxLength={2}
                  style={[styles.dobInput, { backgroundColor: theme.colors.surface }]}
                  error={!!validationErrors.month}
                  placeholder="MM"
                />
                <HelperText type="error" visible={!!validationErrors.month}>
                  {validationErrors.month}
                </HelperText>
              </View>

              <View style={styles.dobSeparator}>
                <Text style={{ color: theme.colors.onSurface }}>/</Text>
              </View>

              <View style={styles.dobInputContainer}>
                <TextInput
                  ref={yearInputRef}
                  label="Year"
                  value={year}
                  onChangeText={text => {
                    const filtered = text.replace(/[^0-9]/g, '');
                    if (filtered.length <= 4) {
                      setYear(filtered);
                    }
                  }}
                  mode="outlined"
                  keyboardType="number-pad"
                  maxLength={4}
                  style={[styles.dobInput, { backgroundColor: theme.colors.surface }]}
                  error={!!validationErrors.year}
                  placeholder="YYYY"
                />
                <HelperText type="error" visible={!!validationErrors.year}>
                  {validationErrors.year}
                </HelperText>
              </View>
            </View>
          </Animated.View>

          <Animated.View entering={FadeInUp.delay(750)}>
            <Text style={[styles.sectionLabel, { color: theme.colors.onSurface }]}>
              Blood Group
            </Text>
            <TouchableOpacity onPress={() => setShowBloodGroupDropdown(true)}>
              <TextInput
                value={bloodGroup === 'unknown' ? 'Select Blood Group' : bloodGroup}
                // onPressIn={() => setShowBloodGroupDropdown(true)}
                mode="outlined"
                right={
                  <TextInput.Icon
                    icon="chevron-down"
                    onPress={() => setShowBloodGroupDropdown(true)}
                  />
                }
                editable={false}
                error={!!validationErrors.bloodGroup}
                style={[styles.input, { backgroundColor: theme.colors.surface }]}
              />
            </TouchableOpacity>

            <HelperText type="error" visible={!!validationErrors.bloodGroup}>
              {validationErrors.bloodGroup}
            </HelperText>

            <View style={styles.consentContainer}>
              <View style={styles.consentTextContainer}>
                <Text style={styles.consentText}>
                  तपाईंको रक्त समूह जानकारीले आपतकालीन अवस्थामा मद्दत गर्न सक्छ। के तपाईं Bolaoo
                  Rides लाई तपाईंको क्षेत्रमा आपतकालीन रक्तदान अनुरोधहरूको लागि तपाईंलाई सम्पर्क
                  गर्न अधिकार दिनुहुन्छ?
                </Text>
              </View>
              <View style={styles.radioCards}>
                <TouchableOpacity
                  style={[styles.radioCard, bloodDonationConsent && styles.radioCardSelected]}
                  onPress={() => setBloodDonationConsent(true)}
                >
                  <View style={styles.radioCardContent}>
                    <Text style={styles.radioCardLabel}>Yes</Text>
                    <View
                      style={[
                        styles.radioCircle,
                        bloodDonationConsent && styles.radioCircleSelected,
                      ]}
                    >
                      {bloodDonationConsent && <View style={styles.radioInnerCircle} />}
                    </View>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.radioCard, !bloodDonationConsent && styles.radioCardSelected]}
                  onPress={() => setBloodDonationConsent(false)}
                >
                  <View style={styles.radioCardContent}>
                    <Text style={styles.radioCardLabel}>No</Text>
                    <View
                      style={[
                        styles.radioCircle,
                        !bloodDonationConsent && styles.radioCircleSelected,
                      ]}
                    >
                      {!bloodDonationConsent && <View style={styles.radioInnerCircle} />}
                    </View>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </Animated.View>
        </View>

        <Animated.View entering={FadeInUp.delay(800)} style={styles.submitContainer}>
          <Button
            mode="contained"
            onPress={handleSubmit}
            loading={isSubmitting}
            disabled={isSubmitting}
            style={styles.submitButton}
            contentStyle={styles.submitButtonContent}
          >
            {existingProfile ? 'Update Profile' : 'Complete Profile'}
          </Button>
        </Animated.View>

        <Modal
          visible={showBloodGroupDropdown}
          onDismiss={() => setShowBloodGroupDropdown(false)}
          style={[styles.modalContainer, { backgroundColor: theme.colors.primary }]}
        >
          <Text style={[styles.modalTitle, { color: theme.colors.onSurface }]}>
            Select Blood Group
          </Text>
          <ScrollView style={styles.modalScroll}>
            {['unknown', 'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'].map(group => (
              <TouchableRipple
                key={group}
                onPress={() => {
                  setBloodGroup(group as BloodGroup);
                  setShowBloodGroupDropdown(false);
                  setValidationErrors(prev => ({ ...prev, bloodGroup: undefined }));
                }}
                style={[
                  styles.bloodGroupItem,
                  bloodGroup === group && { backgroundColor: theme.colors.secondary },
                ]}
              >
                <Text
                  style={[
                    styles.bloodGroupText,
                    { color: bloodGroup === group ? theme.colors.primary : theme.colors.onSurface },
                  ]}
                >
                  {group === 'unknown' ? "Don't Know (थाहा छैन)" : group}
                </Text>
              </TouchableRipple>
            ))}
          </ScrollView>
        </Modal>

        {/* <DatePicker
          modal
          open={showDatePicker}
          date={dob || new Date()}
          onConfirm={handleDateChange}
          onCancel={() => setShowDatePicker(false)}
          mode="date"
          maximumDate={new Date()}
          minimumDate={new Date(1950, 0, 1)}
        /> */}
      </Animated.View>
    );
  };

  if (existingProfileLoading) {
    return <AnimatedLoadingScreen />;
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView
        style={styles.scrollViewStyle}
        contentContainerStyle={styles.scrollViewContainer}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        {renderUserProfileForm()}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollViewStyle: {
    flex: 1,
  },
  scrollViewContainer: {
    padding: 16,
    paddingTop: Platform.OS === 'ios' ? 60 : 32,
  },
  formContainer: {
    flex: 1,
  },
  headerContainer: {
    borderRadius: 24,
    overflow: 'hidden',
    marginBottom: 24,
    elevation: 4,
  },
  headerGradient: {
    padding: 32,
    alignItems: 'center',
  },
  headerContent: {
    alignItems: 'center',
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
    textAlign: 'center',
  },
  formSection: {
    paddingHorizontal: 16,
    // gap: 4,
  },
  input: {
    // marginBottom: 4,
    borderRadius: 12,
  },
  sectionLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginVertical: 8,
  },
  genderContainer: {
    marginTop: 16,
    // marginBottom: 16,
  },
  genderButtons: {
    marginTop: 8,
  },
  segmentButton: {
    borderRadius: 8,
  },
  bloodGroupContainer: {
    marginTop: 16,
  },
  submitContainer: {
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
  },
  submitButton: {
    borderRadius: 12,
    marginVertical: 16,
  },
  submitButtonContent: {
    paddingVertical: 8,
  },
  modalContainer: {
    margin: 20,
    borderRadius: 16,
    padding: 16,
    // maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  modalScroll: {
    // maxHeight: 400,
  },
  bloodGroupItem: {
    padding: 16,
    borderRadius: 12,
    marginVertical: 4,
  },
  bloodGroupText: {
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '500',
  },
  consentContainer: {
    // flexDirection: 'row',
    // alignItems: 'center',
    // justifyContent: 'space-between',
    marginBottom: 16,
  },
  consentTextContainer: {
    flex: 1,
    marginRight: 16,
  },
  consentText: {
    fontSize: 14,
    // color: COLORS.primary,
  },
  radioCards: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
    // marginBottom: 16,
  },
  radioCard: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    padding: 12,
  },
  radioCardSelected: {
    backgroundColor: `${COLORS.primary}15`,
    borderColor: COLORS.primary,
  },
  radioCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  radioCardLabel: {
    fontSize: 14,
    color: COLORS.text,
    fontWeight: '500',
  },
  radioCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioCircleSelected: {
    borderColor: COLORS.primary,
  },
  radioInnerCircle: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: COLORS.primary,
  },
  dobContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  dobInputContainer: {
    flex: 1,
  },
  dobInput: {
    textAlign: 'center',
  },
  dobSeparator: {
    width: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 20,
  },
});
