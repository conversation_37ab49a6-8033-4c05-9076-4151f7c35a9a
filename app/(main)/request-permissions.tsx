import React, { useState, useEffect } from "react";
import { Text } from "react-native-paper";
import { 
  StyleSheet, 
  View, 
  TouchableOpacity, 
  Linking, 
  Platform, 
  Dimensions 
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import useLocationServices from "../hooks/useLocationServices";
import { useTheme } from "@/theme/ThemeContext";
import * as Location from 'expo-location';
import { useRouter } from "expo-router";
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withRepeat, 
  withTiming, 
  Easing,
  interpolate,
  withSequence
} from 'react-native-reanimated';
import { useIsFocused } from "@react-navigation/native";

const { width, height } = Dimensions.get('window');

const RequestPermissions = () => {
  const { requestPermissions } = useLocationServices();
  const { theme } = useTheme();
  const router = useRouter();
  const [permissionStatus, setPermissionStatus] = useState<Location.PermissionStatus | null>(null);

  // Reanimated values
  const mapPinAnimation = useSharedValue(0);
  const containerScale = useSharedValue(1);

  // Animated styles
  const mapPinStyle = useAnimatedStyle(() => {
    const rotation = interpolate(
      mapPinAnimation.value,
      [0, 0.5, 1],
      [0, 20, 0]
    );

    return {
      transform: [
        { rotate: `${rotation}deg` },
        { 
          scale: interpolate(
            mapPinAnimation.value,
            [0, 0.5, 1],
            [1, 1.2, 1]
          )
        }
      ]
    };
  });

  const containerScaleStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: containerScale.value }]
    };
  });

  // Animation setup
  useEffect(() => {
    // Continuous map pin animation
    mapPinAnimation.value = withRepeat(
      withTiming(1, { 
        duration: 2000, 
        easing: Easing.inOut(Easing.ease)
      }),
      -1,
      true
    );

    // Subtle container pulse
    containerScale.value = withRepeat(
      withSequence(
        withTiming(1.02, { duration: 1500 }),
        withTiming(1, { duration: 1500 })
      ),
      -1,
      true
    );
  }, []);

  const isFocused = useIsFocused();

  useEffect(() => {
    const checkPermissionStatus = async () => {
      const isGranted = await requestPermissions();
      setPermissionStatus(isGranted? Location.PermissionStatus.GRANTED : Location.PermissionStatus.DENIED);

      if(isGranted){
        router.replace('/(main)');
      }
     
    };

    checkPermissionStatus();
    
    // Set up interval to continuously check permissions
    // const permissionCheckInterval = setInterval(checkPermissionStatus, 2000);

    // return () => clearInterval(permissionCheckInterval);
  }, [isFocused]);

  const openLocationSettings = () => {
    if (Platform.OS === 'ios') {
      Linking.openURL('app-settings:');
    } else {
      Linking.openSettings();
    }
  };

  const handleRequestPermission = async () => {
    try {
      const result = await requestPermissions();
      
      if (result) {
        // Navigate to dashboard if permissions are granted
        router.replace('/(main)');
      } else {
        // If permissions are not granted, open device settings
        openLocationSettings();
      }
    } catch (error) {
      console.error('Permission request error:', error);
    }
  };

  return (
    <Animated.View 
      style={[
        styles.container, 
        { backgroundColor: theme.colors.background },
        containerScaleStyle
      ]}
    >
      {/* Animated Map Pin */}
      <Animated.View style={[styles.animationContainer, mapPinStyle]}>
        <MaterialCommunityIcons 
          name="map-marker" 
          size={120} 
          color={theme.colors.primary} 
        />
      </Animated.View>
      
      {/* Title and Description */}
      <View style={styles.textContainer}>
        <Text style={[styles.titleText, { color: theme.colors.primary }]}>
          Location Access Needed
        </Text>
        
        <Text style={[styles.descriptionText, { color: theme.colors.onBackground }]}>
          Bolaoo needs your location to provide:
          {'\n'}• Accurate Ride Tracking
          {/* {'\n'}• Nearby Cab Suggestions
          {'\n'}• Precise Pickup/Dropoff */}
        </Text>
      </View>

      {/* Permission Status Indicator */}
      {/* <View style={styles.statusContainer}>
        <MaterialCommunityIcons 
          name={permissionStatus === 'granted' ? 'check-circle' : 'alert-circle'}
          size={40} 
          color={permissionStatus === 'granted' ? theme.colors.primary : theme.colors.error}
        />
        <Text style={[styles.statusText, { 
          color: permissionStatus === 'granted' 
            ? theme.colors.primary 
            : theme.colors.error 
        }]}>
          {permissionStatus === 'granted' 
            ? 'Location Access Granted' 
            : 'Location Access Required'}
        </Text>
      </View> */}
      
      {/* Action Button */}
      <TouchableOpacity
        style={[
          styles.actionButton, 
          { 
            backgroundColor: theme.colors.primary
          }
        ]}
        onPress={handleRequestPermission}
      >
        <Text style={[styles.actionButtonText, { color: theme.colors.onPrimary }]}>
       {permissionStatus === 'granted' ? 'Continue' : 'Grant Location Access'}
        </Text>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  animationContainer: {
    width: width * 0.8,
    height: height * 0.3,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  titleText: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  descriptionText: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  statusText: {
    marginLeft: 10,
    fontSize: 16,
  },
  actionButton: {
    width: width * 0.8,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default RequestPermissions;