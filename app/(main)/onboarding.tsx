import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, Image } from 'react-native';
import { TextInput, HelperText, SegmentedButtons, Surface, Text, Button } from 'react-native-paper';
import { router } from 'expo-router';
import { useUserId } from '@nhost/react';
import { useMutation, useQuery } from '@apollo/client';
import * as DocumentPicker from 'expo-document-picker';
import {
  CREATE_DRIVER,
  UPDATE_DRIVER,
  CREATE_VEHICLE,
  UPDATE_VEHICLE,
  GET_DRIVER,
} from '../graphql/driver';
import { useTheme } from '../../src/theme/ThemeContext';

type OnboardingStep = 'driver' | 'vehicle' | 'documents';

type ValidationErrors = {
  licenseNumber?: string;
  driverName?: string;
  phoneNumber?: string;
  make?: string;
  model?: string;
  year?: string;
  color?: string;
  licensePlate?: string;
};

export default function OnboardingScreen() {
  const userId = useUserId();
  const {
    data: existingDriverData,
    loading: existingDriverLoading,
    refetch: refetchDriver,
  } = useQuery(GET_DRIVER, {
    variables: { userId },
    fetchPolicy: 'network-only',
  });

  const existingDriver = existingDriverData?.drivers[0];
  const existingVehicle = existingDriver?.vehicles[0];

  const { theme } = useTheme();

  const [currentStep, setCurrentStep] = useState<OnboardingStep>('driver');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});

  // Driver Information
  const [licenseNumber, setLicenseNumber] = useState('');
  const [driverName, setDriverName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');

  // Vehicle Information
  const [vehicleType, setVehicleType] = useState('auto');
  const [make, setMake] = useState('');
  const [model, setModel] = useState('');
  const [year, setYear] = useState('');
  const [color, setColor] = useState('');
  const [licensePlate, setLicensePlate] = useState('');

  // Document Upload States
  const [driverLicenseDoc, setDriverLicenseDoc] = useState<string | null>(null);
  const [vehicleRegistrationDoc, setVehicleRegistrationDoc] = useState<string | null>(null);
  const [insuranceDoc, setInsuranceDoc] = useState<string | null>(null);

  const [createDriver, { error: createDriverError, loading: createDriverLoading }] = useMutation(
    CREATE_DRIVER,
    {
      onCompleted: async () => {
        await refetchDriver();
        setValidationErrors({});
        setCurrentStep('vehicle');
      },
    }
  );
  const [updateDriver, { error: updateDriverError, loading: updateDriverLoading }] =
    useMutation(UPDATE_DRIVER);
  const [createVehicle, { error: createVehicleError, loading: createVehicleLoading }] =
    useMutation(CREATE_VEHICLE);
  const [updateVehicle, { error: updateVehicleError, loading: updateVehicleLoading }] =
    useMutation(UPDATE_VEHICLE);

  const isLoading =
    createDriverLoading || updateDriverLoading || createVehicleLoading || updateVehicleLoading;
  const mutationError =
    createDriverError || updateDriverError || createVehicleError || updateVehicleError;

  useEffect(() => {
    if (mutationError) {
      setError(mutationError.message);
    }
  }, [mutationError]);

  useEffect(() => {
    if (existingDriver) {
      setLicenseNumber(existingDriver.license_number || '');
      setDriverName(existingDriver.name || '');
      setPhoneNumber(existingDriver.phone_number || '');

      if (existingVehicle) {
        setVehicleType(existingVehicle.vehicle_type || 'auto');
        setMake(existingVehicle.make || '');
        setModel(existingVehicle.model || '');
        setYear(existingVehicle.year || '');
        setColor(existingVehicle.color || '');
        setLicensePlate(existingVehicle.license_plate || '');
      }
    }
  }, [existingDriver, existingVehicle]);

  // Validation functions
  const validateLicenseNumber = (value: string) => {
    if (!value.trim()) return 'License number is required';
    if (value.length < 6) return 'License number must be at least 6 characters';
    return '';
  };

  const validateDriverName = (value: string) => {
    if (!value.trim()) return 'Driver name is required';
    if (value.length < 3) return 'Name must be at least 3 characters';
    return '';
  };

  const validatePhoneNumber = (value: string) => {
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    if (!value.trim()) return 'Phone number is required';
    if (!phoneRegex.test(value)) return 'Please enter a valid phone number';
    return '';
  };

  const validateMake = (value: string) => {
    if (!value.trim()) return 'Vehicle manufacturer is required';
    return '';
  };

  const validateModel = (value: string) => {
    if (!value.trim()) return 'Vehicle model is required';
    return '';
  };

  const validateYear = (value: string) => {
    const yearNumber = parseInt(value);
    const currentYear = new Date().getFullYear();
    if (!value.trim()) return 'Vehicle year is required';
    if (isNaN(yearNumber)) return 'Please enter a valid year';
    if (yearNumber < 1900 || yearNumber > currentYear + 1)
      return `Year must be between 1900 and ${currentYear + 1}`;
    return '';
  };

  const validateColor = (value: string) => {
    if (!value.trim()) return 'Vehicle color is required';
    return '';
  };

  const validateLicensePlate = (value: string) => {
    if (!value.trim()) return 'License plate is required';
    if (value.length < 2) return 'License plate must be at least 2 characters';
    return '';
  };

  const validateDriverForm = () => {
    const errors: ValidationErrors = {};

    const licenseError = validateLicenseNumber(licenseNumber);
    const nameError = validateDriverName(driverName);
    const phoneError = validatePhoneNumber(phoneNumber);

    if (licenseError) errors.licenseNumber = licenseError;
    if (nameError) errors.driverName = nameError;
    if (phoneError) errors.phoneNumber = phoneError;

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateVehicleForm = () => {
    const errors: ValidationErrors = {};

    const makeError = validateMake(make);
    const modelError = validateModel(model);
    const yearError = validateYear(year);
    const colorError = validateColor(color);
    const licensePlateError = validateLicensePlate(licensePlate);

    if (makeError) errors.make = makeError;
    if (modelError) errors.model = modelError;
    if (yearError) errors.year = yearError;
    if (colorError) errors.color = colorError;
    if (licensePlateError) errors.licensePlate = licensePlateError;

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleDriverSubmit = async () => {
    if (!userId) return;

    if (!validateDriverForm()) {
      setError('Please fix the validation errors before continuing.');
      return;
    }

    try {
      setIsSubmitting(true);
      setError('');

      const driverInput = {
        license_number: licenseNumber.trim(),
        name: driverName.trim(),
        phone_number: phoneNumber.trim(),
      };

      if (existingDriver?.id) {
        await updateDriver({
          variables: {
            driverId: existingDriver?.id,
            data: driverInput,
          },
        });
      } else {
        await createDriver({
          variables: {
            userId,
            ...driverInput,
          },
        });
      }
      setValidationErrors({});
      setCurrentStep('vehicle');
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'An error occurred while saving driver information'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleVehicleSubmit = async () => {
    if (!existingDriver?.id) {
      setError('Please save driver information before saving vehicle information.');
      return;
    }

    if (!validateVehicleForm()) {
      setError('Please fix the validation errors before continuing.');
      return;
    }

    try {
      setIsSubmitting(true);
      setError('');

      const vehicleInput = {
        vehicleType,
        make,
        model,
        year,
        color,
        licensePlate,
      };

      if (existingVehicle?.id) {
        await updateVehicle({
          variables: {
            vehicleId: existingVehicle?.id,
            ...vehicleInput,
          },
        });
      } else {
        await createVehicle({
          variables: {
            driverId: existingDriver?.id,
            ...vehicleInput,
          },
        });
      }

      await updateDriver({
        variables: {
          driverId: existingDriver?.id,
          data: { verification_status: 'processing' },
        },
      });
      router.replace('/(main)');

      // setCurrentStep('documents');
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'An error occurred while saving vehicle information'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDocumentUpload = async (documentType: string) => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/pdf', 'image/*'],
      });

      // Check if we have any selected files
      if (result.assets && result.assets.length > 0) {
        const fileUri = result.assets[0].uri;

        // Here you would typically upload to your storage
        // For now, we'll just store the URI
        switch (documentType) {
          case 'license':
            setDriverLicenseDoc(fileUri);
            break;
          case 'registration':
            setVehicleRegistrationDoc(fileUri);
            break;
          case 'insurance':
            setInsuranceDoc(fileUri);
            break;
        }
      } else {
        // User cancelled the picker
        // console.log('Document picking cancelled');
      }
    } catch (error) {
      setError(
        error instanceof Error ? error.message : 'An error occurred while selecting document'
      );
    }
  };

  const handleSubmitDocuments = async () => {
    if (!existingDriver?.id) return;

    try {
      setIsSubmitting(true);
      setError('');

      // Update driver status to processing
      await updateDriver({
        variables: {
          driverId: existingDriver?.id,
          data: { verification_status: 'processing' },
        },
      });

      // Here you would typically:
      // 1. Upload documents to storage
      // 2. Create document records in the database

      router.replace('/(main)/pending-approval');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while submitting documents');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderDriverForm = () => (
    <View style={styles.formContainer}>
      <Text variant="headlineMedium" style={styles.stepTitle}>
        Driver Information
      </Text>
      <TextInput
        mode="outlined"
        label="Driver's License Number"
        value={licenseNumber}
        onChangeText={text => {
          setLicenseNumber(text);
          setValidationErrors(prev => ({ ...prev, licenseNumber: '' }));
        }}
        error={!!validationErrors.licenseNumber}
        style={styles.input}
        activeOutlineColor={theme.colors.primary}
      />
      {validationErrors.licenseNumber && (
        <HelperText type="error" visible={!!validationErrors.licenseNumber}>
          {validationErrors.licenseNumber}
        </HelperText>
      )}

      <TextInput
        mode="outlined"
        label="Name"
        value={driverName}
        onChangeText={text => {
          setDriverName(text);
          setValidationErrors(prev => ({ ...prev, driverName: '' }));
        }}
        error={!!validationErrors.driverName}
        style={styles.input}
        activeOutlineColor={theme.colors.primary}
      />
      {validationErrors.driverName && (
        <HelperText type="error" visible={!!validationErrors.driverName}>
          {validationErrors.driverName}
        </HelperText>
      )}

      <TextInput
        mode="outlined"
        label="Phone Number"
        value={phoneNumber}
        keyboardType="phone-pad"
        onChangeText={text => {
          setPhoneNumber(text);
          setValidationErrors(prev => ({ ...prev, phoneNumber: '' }));
        }}
        error={!!validationErrors.phoneNumber}
        style={styles.input}
        activeOutlineColor={theme.colors.primary}
      />
      {validationErrors.phoneNumber && (
        <HelperText type="error" visible={!!validationErrors.phoneNumber}>
          {validationErrors.phoneNumber}
        </HelperText>
      )}

      {error ? (
        <HelperText type="error" visible={!!error}>
          {error}
        </HelperText>
      ) : null}

      <Button
        mode="contained"
        onPress={handleDriverSubmit}
        style={styles.button}
        contentStyle={styles.buttonContent}
        loading={isSubmitting}
        disabled={isSubmitting}
        icon="check-circle"
        buttonColor={theme.colors.primary}
      >
        Next
      </Button>
    </View>
  );

  const renderVehicleForm = () => (
    <View style={styles.formContainer}>
      <Text variant="headlineMedium" style={styles.stepTitle}>
        Vehicle Information
      </Text>
      <SegmentedButtons
        value={vehicleType}
        onValueChange={setVehicleType}
        buttons={[
          {
            value: 'auto',
            label: 'Auto-Rickshaw',
            checkedColor: 'white',
            style: {
              backgroundColor: vehicleType === 'auto' ? theme.colors.primary : undefined,
            },
          },
          {
            value: 'toto',
            label: 'E-Rickshaw',
            checkedColor: 'white',
            style: {
              backgroundColor: vehicleType === 'toto' ? theme.colors.primary : undefined,
            },
          },
        ]}
        style={styles.segmentedButton}
      />
      <TextInput
        mode="outlined"
        label="Vehicle Manufacturer"
        value={make}
        onChangeText={setMake}
        style={styles.input}
        activeOutlineColor={theme.colors.primary}
        error={!!validationErrors.make}
      />
      {validationErrors.make && (
        <HelperText type="error" visible={!!validationErrors.make}>
          {validationErrors.make}
        </HelperText>
      )}
      <TextInput
        mode="outlined"
        label="Model"
        value={model}
        onChangeText={setModel}
        style={styles.input}
        activeOutlineColor={theme.colors.primary}
        error={!!validationErrors.model}
      />
      {validationErrors.model && (
        <HelperText type="error" visible={!!validationErrors.model}>
          {validationErrors.model}
        </HelperText>
      )}
      <TextInput
        mode="outlined"
        label="Year"
        value={year}
        onChangeText={setYear}
        keyboardType="number-pad"
        style={styles.input}
        activeOutlineColor={theme.colors.primary}
        error={!!validationErrors.year}
      />
      {validationErrors.year && (
        <HelperText type="error" visible={!!validationErrors.year}>
          {validationErrors.year}
        </HelperText>
      )}
      <TextInput
        mode="outlined"
        label="Color"
        value={color}
        onChangeText={setColor}
        style={styles.input}
        activeOutlineColor={theme.colors.primary}
        error={!!validationErrors.color}
      />
      {validationErrors.color && (
        <HelperText type="error" visible={!!validationErrors.color}>
          {validationErrors.color}
        </HelperText>
      )}
      <TextInput
        mode="outlined"
        label="License Plate"
        value={licensePlate}
        onChangeText={setLicensePlate}
        style={styles.input}
        activeOutlineColor={theme.colors.primary}
        error={!!validationErrors.licensePlate}
      />
      {validationErrors.licensePlate && (
        <HelperText type="error" visible={!!validationErrors.licensePlate}>
          {validationErrors.licensePlate}
        </HelperText>
      )}

      <View style={styles.buttonContainer}>
        <Button
          mode="outlined"
          onPress={() => setCurrentStep('driver')}
          style={[styles.button, styles.backButton]}
          contentStyle={styles.buttonContent}
          disabled={isSubmitting}
          icon="arrow-left"
        >
          Back
        </Button>
        <Button
          mode="contained"
          onPress={handleVehicleSubmit}
          style={[styles.button, styles.nextButton]}
          contentStyle={styles.buttonContent}
          loading={isSubmitting}
          disabled={isSubmitting}
          icon="check-circle"
          buttonColor={theme.colors.primary}
        >
          Next
        </Button>
      </View>
    </View>
  );

  const renderDocumentsForm = () => (
    <View style={styles.formContainer}>
      <Text variant="headlineMedium" style={styles.stepTitle}>
        Required Documents
      </Text>
      <Text variant="bodyMedium" style={styles.subtitle}>
        Please upload clear photos or scans of the following documents
      </Text>

      <Button
        mode="outlined"
        onPress={() => handleDocumentUpload('license')}
        style={styles.documentButton}
      >
        {driverLicenseDoc ? " Driver's License" : "Upload Driver's License"}
      </Button>

      <Button
        mode="outlined"
        onPress={() => handleDocumentUpload('registration')}
        style={styles.documentButton}
      >
        {vehicleRegistrationDoc ? ' Vehicle Registration' : 'Upload Vehicle Registration'}
      </Button>

      <Button
        mode="outlined"
        onPress={() => handleDocumentUpload('insurance')}
        style={styles.documentButton}
      >
        {insuranceDoc ? ' Insurance Document' : 'Upload Insurance Document'}
      </Button>

      <Button
        mode="contained"
        onPress={handleSubmitDocuments}
        style={[styles.button, styles.submitButton]}
        contentStyle={styles.buttonContent}
        loading={isSubmitting}
        disabled={isSubmitting}
        icon="check-circle"
        buttonColor={theme.colors.primary}
      >
        Skip for now
      </Button>
    </View>
  );

  // if (existingDriverLoading || isLoading) {
  //   return (
  //     <View style={[styles.container, styles.centerContent]}>
  //       <Text>Loading...</Text>
  //     </View>
  //   );
  // }

  if (error) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={styles.errorText}>{error}</Text>
        <Button mode="contained" onPress={() => setError(null)} style={styles.errorButton}>
          Try Again
        </Button>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView
        style={styles.scrollViewStyle}
        contentContainerStyle={[styles.scrollContainer]}
        keyboardShouldPersistTaps="handled"
      >
        <Surface style={[styles.card, { backgroundColor: theme.colors.surface }]} elevation={2}>
          <View style={styles.logoContainer}>
            <Image
              source={require('../../assets/images/splash-icon.png')}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>

          <Text variant="headlineLarge" style={[styles.title, { color: theme.colors.primary }]}>
            Driver Onboarding
          </Text>
          <Text variant="bodyLarge" style={[styles.subtitle, { color: theme.colors.onBackground }]}>
            Complete your profile to start accepting rides
          </Text>

          {error ? (
            <HelperText type="error" visible={!!error}>
              {error}
            </HelperText>
          ) : null}

          {currentStep === 'driver' && renderDriverForm()}
          {currentStep === 'vehicle' && renderVehicleForm()}
          {currentStep === 'documents' && renderDocumentsForm()}
        </Surface>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollViewStyle: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
  },
  card: {
    padding: 24,
    borderRadius: 12,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  logo: {
    width: 120,
    height: 120,
  },
  title: {
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: 32,
    opacity: 0.7,
  },
  formContainer: {
    gap: 16,
  },
  stepTitle: {
    marginBottom: 16,
    fontWeight: '600',
  },
  input: {
    marginBottom: 8,
  },
  segmentedButton: {
    marginBottom: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  button: {
    marginVertical: 8,
    borderRadius: 8,
    height: 56,
  },
  buttonContent: {
    height: 56,
    paddingHorizontal: 16,
  },
  backButton: {
    marginTop: 8,
  },
  nextButton: {
    flex: 1,
  },
  documentButton: {
    marginVertical: 8,
  },
  submitButton: {
    marginTop: 30,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: 'red',
    marginBottom: 16,
    textAlign: 'center',
  },
  errorButton: {
    marginTop: 8,
  },
});
