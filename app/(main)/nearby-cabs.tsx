import React, { useC<PERSON>back, useMemo, useState } from 'react';
import { StyleSheet, View, Alert } from 'react-native';
import { Text, Surface, IconButton, Chip, Button } from 'react-native-paper';
import { router } from 'expo-router';
import useDriverLocations from '../hooks/useDriverLocations';
import { useLocalSearchParams } from 'expo-router';
import { useTheme } from '../../src/theme/ThemeContext';
import { useTranslation } from 'react-i18next';
import NearbyVechicleCard from '../components/NearbyVechicleCard';
import { FlashList } from '@shopify/flash-list';
// import AsyncStorage from '@react-native-async-storage/async-storage';
// import { getPickupAddress } from 'app/utils/location';
// import { useMutation } from '@apollo/client';
// import { CREATE_FREE_RIDE_REQUEST } from 'app/graphql/nearby';
// import { sendtoDriverNotification } from 'app/utils/notification-sender';

const ESTIMATED_ITEM_SIZE = 200;
const POLL_INTERVAL = 60000; // 30 seconds

interface Driver {
  id: string;
  location: {
    type: string;
    coordinates: [number, number];
  };
  driver: {
    id: string;
    name: string;
    phone_number: string;
    vehicles: Array<{
      id: string;
      license_plate: string;
      model: string;
      color: string;
      make: string;
      vehicle_type: string;
    }>;
  };
}

const DISTANCE_FILTERS = [
  { label: '1km', value: 1 },
  { label: '3km', value: 3 },
  { label: '5km', value: 5 },
  { label: 'All', value: Infinity },
];

const NearbyCabs = () => {
  const { t } = useTranslation();
  const { latitude, longitude } = useLocalSearchParams();
  const { theme } = useTheme();
  const [selectedDistance, setSelectedDistance] = useState<number>(5);
  // const [isRequestingAll, setIsRequestingAll] = useState(false);

  const userLocation = useMemo(
    () => ({
      latitude: Number(latitude),
      longitude: Number(longitude),
    }),
    [latitude, longitude]
  );

  const {
    driverLocations,
    loading: driversLoading,
    refetch: driversRefetch,
  } = useDriverLocations(POLL_INTERVAL, [userLocation.latitude, userLocation.longitude]);

  const calculateDistance = useCallback(
    (driverLocation: { type: string; coordinates: [number, number] }) => {
      const [driverLng, driverLat] = driverLocation.coordinates;
      const R = 6371; // Earth's radius in km
      const dLat = ((driverLat - userLocation.latitude) * Math.PI) / 180;
      const dLon = ((driverLng - userLocation.longitude) * Math.PI) / 180;
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos((userLocation.latitude * Math.PI) / 180) *
          Math.cos((driverLat * Math.PI) / 180) *
          Math.sin(dLon / 2) *
          Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      return R * c; // Distance in km
    },
    [userLocation]
  );

  const filteredDrivers = useMemo(() => {
    if (!driverLocations) return [];

    // First calculate distances for all drivers
    const driversWithDistance = driverLocations.map((driver: Driver) => ({
      ...driver,
      distance: calculateDistance(driver.location),
    }));

    // Filter by selected distance
    const filtered = driversWithDistance.filter(
      (driver: { distance: number }) => driver.distance <= selectedDistance
    );

    // Sort by distance (closest first)
    return filtered.sort(
      (a: { distance: number }, b: { distance: number }) => a.distance - b.distance
    );
  }, [driverLocations, selectedDistance, calculateDistance]);

  const driverCount = useMemo(() => filteredDrivers.length ?? 0, [filteredDrivers]);

  const renderItem = useCallback(
    ({ item }: { item: Driver }) => (
      <NearbyVechicleCard key={item.id} item={item} userLocation={userLocation} />
    ),
    [userLocation]
  );

  const renderSeparator = useCallback(() => <View style={styles.separator} />, []);

  const renderEmpty = useCallback(
    () => (
      <View style={styles.centerContent}>
        <Text variant="bodyLarge">
          {driversLoading ? t('nearbyCabs.loading') : t('nearbyCabs.noDrivers')}
        </Text>
      </View>
    ),
    [driversLoading, t]
  );

  const keyExtractor = useCallback((item: any) => item.id, []);

  const headerTitle = useMemo(() => `${t('nearbyCabs.title')} (${driverCount})`, [t, driverCount]);

  // const [createFreeRideRequest, { loading: createFreeRideRequestLoading }] = useMutation(
  //   CREATE_FREE_RIDE_REQUEST,
  //   {
  //     // onCompleted: () => {
  //     //   !onClose && router.back();
  //     // },
  //   }
  // );

  // const handleRequestAll = useCallback(async () => {
  //   setIsRequestingAll(true);
  //   try {
  //     const driversWithin3km = filteredDrivers.filter(
  //       (driver: { distance: number }) => driver.distance <= 3
  //     );

  //     // Send requests sequentially to avoid overwhelming the server
  //     for (const driver of driversWithin3km) {
  //       const pickupAddress = await getPickupAddress(
  //         Number(userLocation.latitude),
  //         Number(userLocation.longitude)
  //       );

  //       await createFreeRideRequest({
  //         variables: {
  //           input: {
  //             driver_id: driver.driver.id,
  //             profile_id: await AsyncStorage.getItem('profileId'),
  //             pickup_location: {
  //               type: 'Point',
  //               coordinates: [Number(userLocation.longitude), Number(userLocation.latitude)],
  //             },
  //             pickup_address: pickupAddress || null,
  //           },
  //         },
  //       });

  //       // Send notification to driver
  //       if (driver.driver.notification_token) {
  //         await sendtoDriverNotification(
  //           driver.driver.notification_token,
  //           'You have a new Ride request',
  //           'Respond quickly!'
  //         );
  //       }
  //     }

  //     // Show success message
  //     Alert.alert('Success', `Sent requests to ${driversWithin3km.length} nearby drivers`);

  //     // Go back to map
  //     router.back();
  //   } catch (error) {
  //     console.error('Error requesting all rides:', error);
  //     Alert.alert('Error', 'Failed to send some requests. Please try again.');
  //   } finally {
  //     setIsRequestingAll(false);
  //   }
  // }, [filteredDrivers, userLocation, createFreeRideRequest]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <Surface
          style={[styles.headerCard, { backgroundColor: theme.colors.surface }]}
          elevation={4}
        >
          <View style={styles.headerLeft}>
            <IconButton
              icon="arrow-left"
              size={24}
              iconColor={theme.colors.primary}
              onPress={() => router.back()}
            />
            <Text
              variant="titleLarge"
              style={[styles.headerTitle, { color: theme.colors.onSurface }]}
            >
              {headerTitle}
            </Text>
          </View>
        </Surface>
      </View>

      <View style={styles.filterContainer}>
        {DISTANCE_FILTERS.map(filter => (
          <Chip
            key={filter.value}
            selected={selectedDistance === filter.value}
            onPress={() => setSelectedDistance(filter.value)}
            style={styles.filterChip}
            showSelectedOverlay={true}
            mode="outlined"
          >
            {filter.label}
          </Chip>
        ))}

        {/* Add Request All button */}
        {/* <Button
          mode="contained-tonal"
          onPress={handleRequestAll}
          loading={isRequestingAll}
          disabled={isRequestingAll || filteredDrivers.length === 0}
          style={styles.requestAllButton}
          icon="rocket-launch"
        >
          Request All (3km)
        </Button> */}
      </View>

      <FlashList
        data={filteredDrivers}
        renderItem={renderItem}
        estimatedItemSize={ESTIMATED_ITEM_SIZE}
        ItemSeparatorComponent={renderSeparator}
        keyExtractor={keyExtractor}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshing={driversLoading}
        onRefresh={driversRefetch}
        ListEmptyComponent={renderEmpty}
        extraData={[userLocation, driverCount]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 4,
    gap: 8,
  },
  filterChip: {
    marginRight: 4,
  },
  headerCard: {
    borderRadius: 12,
    padding: 8,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    marginLeft: 8,
  },
  separator: {
    height: 14,
    width: '100%',
  },
  listContent: {
    padding: 16,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  requestAllButton: {
    marginLeft: 'auto',
    borderRadius: 20,
  },
});

export default React.memo(NearbyCabs);
