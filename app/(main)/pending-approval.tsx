import React from 'react';
import { StyleSheet, View, ScrollView, Linking } from 'react-native';
import { Text, Button, Surface } from 'react-native-paper';
import { useSignOut } from '@nhost/react';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useApolloClient } from '@apollo/client';
import { COLORS } from '@/constants';

export default function PendingApprovalScreen() {
  const { signOut } = useSignOut();
  const client = useApolloClient();

  const handleSignOut = async () => {
    try {
      await signOut();
      await client.clearStore();
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Error during log out:', error);
    }
  };

  const handleContact = () => {
    Linking.openURL('tel:+*************');
  };

  return (
    <ScrollView
      contentContainerStyle={[styles.scrollContainer, { backgroundColor: COLORS.background }]}
      keyboardShouldPersistTaps="handled"
    >
      <Surface style={styles.container} elevation={4}>
        <View style={styles.header}>
          <MaterialCommunityIcons
            name="account-clock"
            size={80}
            color={COLORS.primary}
            style={styles.icon}
          />
          <Text variant="headlineMedium" style={styles.title}>
            Application Pending
          </Text>
          <Text variant="bodyLarge" style={styles.subtitle}>
            Your application is under review
          </Text>
          <Text variant="bodyMedium" style={styles.description}>
            We'll notify you once your account is approved. This usually takes 1-2 business days.
          </Text>
        </View>

        <View style={styles.content}>
          <Text variant="bodyMedium" style={styles.additionalInfo}>
            While you wait:
          </Text>
          <View style={styles.bulletPoints}>
            <Text style={styles.bulletPoint}>• Make sure your phone number is up to date</Text>
            <Text style={styles.bulletPoint}>• Keep your documents ready for verification</Text>
            <Text style={styles.bulletPoint}>• Check your email regularly for updates</Text>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <Button
            mode="contained"
            onPress={handleContact}
            style={styles.button}
            contentStyle={styles.buttonContent}
            icon="phone"
            buttonColor={COLORS.primary}
          >
            Contact Support
          </Button>

          <Button
            mode="outlined"
            onPress={handleSignOut}
            style={styles.button}
            contentStyle={styles.buttonContent}
            icon="logout"
          >
            Log Out
          </Button>
        </View>
      </Surface>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    padding: 16,
  },
  container: {
    padding: 24,
    borderRadius: 12,
    backgroundColor: COLORS.surface,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  icon: {
    marginBottom: 16,
  },
  title: {
    color: COLORS.primary,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    color: COLORS.text,
    marginBottom: 8,
    fontWeight: '500',
    textAlign: 'center',
  },
  description: {
    color: COLORS.text,
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: 24,
  },
  content: {
    marginBottom: 32,
  },
  additionalInfo: {
    color: COLORS.text,
    fontWeight: '600',
    marginBottom: 12,
  },
  bulletPoints: {
    marginLeft: 8,
  },
  bulletPoint: {
    color: COLORS.text,
    opacity: 0.8,
    marginBottom: 8,
    lineHeight: 24,
  },
  buttonContainer: {
    gap: 16,
  },
  button: {
    height: 56,
    borderRadius: 8,
    // backgroundColor: COLORS.primary,
  },
  buttonContent: {
    height: 56,
    paddingHorizontal: 16,
  },
});
