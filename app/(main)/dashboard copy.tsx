import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, View, ScrollView,  ActivityIndicator, Animated } from 'react-native';
import {  Text, Switch, Button, Avatar,  IconButton, Surface } from 'react-native-paper';
import { router } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {  useUserId } from '@nhost/react';

import useLocationServices from '../hooks/useLocationServices';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../../src/theme/ThemeContext';
import { useQuery, useMutation, gql } from '@apollo/client';
import MapView from 'app/components/MapView';

// Verify the query matches exactly
const USER_PROFILE_QUERY = gql`
  query GetUserProfile($userId: uuid!) {
    user_profiles(where: { user_id: { _eq: $userId } }) {
      id
      full_name
      phone
      avatar_url
      metadata
      is_active
      last_seen_at
    }
  }
`;

export default function DashboardScreen() {
  const { theme } = useTheme();
  const userId = useUserId();
  const { data: existingProfileData, loading: profileLoading, error: profileError } = useQuery(USER_PROFILE_QUERY, {
    variables: { userId },
    fetchPolicy: 'network-only',
  });

  const profileData = existingProfileData?.user_profiles[0];





  if (profileLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.onBackground }]}>Loading...</Text>
      </View>
    );
  }

  if (profileError) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: theme.colors.background }]}>
        <MaterialCommunityIcons name="alert-circle" size={48} color={theme.colors.error} />
        <Text style={[styles.errorText, { color: theme.colors.error }]}>Error loading profile data</Text>
        <Button mode="contained" buttonColor={theme.colors.primary} textColor={theme.colors.onPrimary} onPress={() => router.replace('/(auth)/login')}>
          Return to Login
        </Button>
      </View>
    );
  }


  if (!profileData) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.onBackground }]}>Initializing profile profile...</Text>
      </View>
    );
  }

  return (
    <View 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      // contentContainerStyle={styles.scrollContent}
    >
      <View style={styles.header}>
        <Surface style={[styles.headerCard, { backgroundColor: theme.colors.surface }]} elevation={4}>
          <Avatar.Icon 
            size={48} 
            icon="account" 
            style={{ backgroundColor: 'transparent' }} 
            color={theme.colors.primary}
          />
          <View style={styles.headerText}>
            <View style={styles.nameContainer}>
              <Text variant="titleLarge" style={[styles.headerTitle, { color: theme.colors.onSurface }]}>
                {profileData?.full_name || 'profile'}
              </Text>
              {/* <Surface style={[
                styles.verificationBadge, 
                { backgroundColor: profileData?.verification_status === 'verified' ? theme.colors.primary : theme.colors.onSurface }
              ]}>
                <MaterialCommunityIcons
                  name={profileData?.verification_status === 'verified' ? 'check-circle' : 'alert-circle'}
                  size={16}
                  color={theme.colors.surface}
                  style={styles.verificationIcon}
                />
                <Text style={[styles.verificationText, { color: theme.colors.surface }]}>
                  {profileData?.verification_status === 'verified' ? 'Verified' : 'Not Verified'}
                </Text>
              </Surface> */}
            </View>
            <Text variant="bodyMedium" style={[styles.headerSubtitle, { color: theme.colors.onSurfaceVariant }]}>
              Ready to start your day?
            </Text>
          </View>

          <IconButton 
            icon="cog" 
            size={28}
            iconColor={theme.colors.onSurface}
            style={styles.iconButton}
            onPress={() => router.push('/(main)/settings')} 
          />
        </Surface>
      </View>

      {/* <View style={styles.statusContainer}>
        <Surface style={[styles.statusCard, { backgroundColor: theme.colors.surface }]} elevation={2}>
          <View style={styles.statusContent}>
            <View style={styles.statusLeft}>
              <MaterialCommunityIcons
                name={isOnline ? 'circle-slice-8' : 'circle-outline'}
                size={24}
                color={theme.colors.primary}
              />
              <Text variant="titleMedium" style={[styles.statusText, { color: theme.colors.onSurface }]}>
                {isOnline ? 'Online' : 'Offline'}
              </Text>
            </View>
            <Switch
              value={isOnline}
              onValueChange={handleStatusChange}
              color={theme.colors.primary}
            />
          </View>
          {isBackgroundTracking && (
            <View style={styles.trackingStatus}>
              <View style={styles.pulseContainer}>
                <Animated.View style={[
                  styles.pulse,
                  {
                    opacity: blinkAnim,
                    backgroundColor: theme.colors.primary
                  }
                ]} />
              </View>
              <Text style={[styles.trackingText, { color: theme.colors.onSurfaceVariant }]}>
                Location tracking active
              </Text>
            </View>
          )}
        </Surface>
      </View> */}

      <View style={styles.mapContainer}>
      <MapView latitude={0} longitude={0} />
      </View>

      {/* <View style={styles.statsContainer}>
        <Surface style={[styles.statsCard, { backgroundColor: theme.colors.surface }]} elevation={2}>
          <View style={styles.stat}>
            <MaterialCommunityIcons name="card-account-details" size={24} color={theme.colors.primary} />
            <Text style={[styles.statValue, { color: theme.colors.onSurface }]}>
              {profile?.phone_number || 'N/A'}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.onSurfaceVariant }]}>Phone Number</Text>
            <Text style={[styles.statValue, { color: theme.colors.onSurface }]}>
              {profile?.name || 'N/A'}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.onSurfaceVariant }]}>Name</Text>
          </View>
          <View style={[styles.statDivider, { backgroundColor: theme.colors.onSurface }]} />
          <View style={styles.stat}>
            <MaterialCommunityIcons name="car" size={24} color={theme.colors.primary} />
          
            <Text style={[styles.statValue, { color: theme.colors.onSurface }]}>  
             {vehicle?.license_plate}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.onSurfaceVariant }]}>Vehicle Number</Text>
            <Text style={[styles.statValue, { color: theme.colors.onSurface }]}>
            {vehicle?.color} {vehicle?.make} {vehicle?.model}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.onSurfaceVariant }]}>Modal</Text>
          </View>
        </Surface>
      </View> */}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginVertical: 16,
  },
  header: {
    // marginBottom: 16,
  },
  headerCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
  },
  headerText: {
    flex: 1,
    // marginLeft: 16,
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerTitle: {
    fontWeight: 'bold',
  },
  headerSubtitle: {
    opacity: 0.7,
  },
  verificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  verificationIcon: {
    marginRight: 4,
  },
  verificationText: {
    fontSize: 12,
    fontWeight: '500',
  },
  iconButton: {
    margin: 0,
  },
  statusContainer: {
    marginBottom: 16,
  },
  statusCard: {
    padding: 16,
    borderRadius: 12,
  },
  statusContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statusLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusText: {
    fontWeight: '500',
  },
  trackingStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    gap: 8,
  },
  pulseContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pulse: {
    position: 'absolute',
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  trackingText: {
    fontSize: 14,
    opacity: 0.7,
  },
  mapContainer: {
    // height: 600,
    flex:1,
    marginVertical: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  statsContainer: {
    marginBottom: 16,
  },
  statsCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
  },
  stat: {
    flex: 1,
    alignItems: 'center',
  },
  statDivider: {
    width: 1,
    opacity: 0.1,
    marginHorizontal: 16,
  },
  statValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  statLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  placeholderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  placeholderTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  placeholderText: {
    fontSize: 14,
    opacity: 0.7,
  },
});
