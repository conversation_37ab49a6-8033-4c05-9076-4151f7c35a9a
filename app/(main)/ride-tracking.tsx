import React, { useEffect, useState, useCallback, useMemo } from 'react';
import {
  StyleSheet,
  View,
  Dimensions,
  BackHandler,
  <PERSON>ing,
  <PERSON><PERSON>,
  ScrollView,
} from 'react-native';
import {
  Text,
  Surface,
  Avatar,
  IconButton,
  Button,
  Divider,
  ActivityIndicator,
  Portal,
  Modal,
  RadioButton,
  TextInput,
  Title,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { WebView } from 'react-native-webview';
import { useLocalSearchParams, router } from 'expo-router';
import { useTheme } from '../../src/theme/ThemeContext';
import { gql, useQuery, useMutation } from '@apollo/client';
import useLocationServices from 'app/hooks/useLocationServices';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useLocation } from 'app/contexts/LocationContext';
import { useRoute } from '../contexts/RouteContext';
import { t } from 'i18next';

const GET_RIDE = gql`
  query GetRide($id: uuid!) {
    rides_by_pk(id: $id) {
      id
      status
      pickup_location
      dropoff_location
      pickup_address
      dropoff_address
      estimated_distance
      estimated_duration
      estimated_fare
      driver {
        id
        name
        phone_number
        average_rating
        ratings_aggregate {
          aggregate {
            avg {
              rating
            }
          }
        }
        completedRides: free_rides_aggregate(where: { status: { _eq: "completed" } }) {
          aggregate {
            count
          }
        }
        totalRides: free_rides_aggregate {
          aggregate {
            count
          }
        }
      }
      vehicles {
        id
        make
        model
        color
        license_plate
      }
    }
  }
`;

const UPDATE_RIDE_STATUS = gql`
  mutation UpdateRideStatus($rideId: uuid!, $status: ride_status_enum!, $cancelReason: String) {
    update_rides_by_pk(
      pk_columns: { id: $rideId }
      _set: { status: $status, cancel_reason: $cancelReason }
    ) {
      id
      status
    }
  }
`;

const RideTracking = () => {
  const { rideId } = useLocalSearchParams();
  const { theme } = useTheme();
  const { routeGeometry, clearRouteInfo } = useRoute();
  const [isMapReady, setIsMapReady] = useState(false);
  const webViewRef = React.useRef<WebView>(null);
  const { currentLocation, startForegroundTracking } = useLocationServices();
  const { setDropoffLocation } = useLocation();
  // const initialLocation = useMemo(() => {
  //   if (currentLocation?.coords) {
  //     return {
  //       lat: currentLocation.coords.latitude,
  //       lng: currentLocation.coords.longitude
  //     };
  //   }
  //   return { lat: 27.7172, lng: 85.3240 }; // Default to Kathmandu
  // }, [currentLocation]);

  useEffect(() => {
    const initLocation = async () => {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.error('Permission to access location was denied');
        return;
      }
      await startForegroundTracking();
    };

    initLocation();
  }, [startForegroundTracking]);

  const { data, loading, error } = useQuery(GET_RIDE, {
    variables: { id: rideId },
    pollInterval: 30000, // Poll every 30 seconds for updates
  });

  const [updateRideStatus] = useMutation(UPDATE_RIDE_STATUS);

  const ride = data?.rides_by_pk;

  const updateMap = useCallback(() => {
    if (!webViewRef.current || !isMapReady || !ride) {
      console.log('Map not ready:', { webViewRef: !!webViewRef.current, isMapReady, ride: !!ride });
      return;
    }

    const script = `
      try {
        const map = window.map;
        
        // Clear existing markers and route
        if (window.markers) {
          window.markers.forEach(marker => marker.remove());
          window.markers = [];
        } else {
          window.markers = [];
        }
        
        if (window.routeLine) {
          window.routeLine.remove();
          window.routeLine = null;
        }

        // Add pickup marker
        const pickupMarker = L.marker([
          ${ride.pickup_location.coordinates[1]}, 
          ${ride.pickup_location.coordinates[0]}
        ], {
          icon: L.divIcon({
            className: 'custom-div-icon',
            html: '<div style="background-color: #4CAF50; width: 15px; height: 15px; border-radius: 50%; border: 2px solid white;"></div>',
            iconSize: [15, 15],
          })
        }).addTo(map);
        window.markers.push(pickupMarker);

        // Add dropoff marker
        const dropoffMarker = L.marker([
          ${ride.dropoff_location.coordinates[1]}, 
          ${ride.dropoff_location.coordinates[0]}
        ], {
          icon: L.divIcon({
            className: 'custom-div-icon',
            html: '<div style="background-color: #F44336; width: 15px; height: 15px; border-radius: 50%; border: 2px solid white;"></div>',
            iconSize: [15, 15],
          })
        }).addTo(map);
        window.markers.push(dropoffMarker);

        ${
          routeGeometry
            ? `
          // Add route line
          const routeCoords = ${JSON.stringify((routeGeometry as any)?.coordinates)}.map(coord => [coord[1], coord[0]]);
      
          window.routeLine = L.polyline(routeCoords, {
            color: '#2196F3',
            weight: 4,
            opacity: 0.8
          }).addTo(map);
          
          // Fit bounds to show entire route
          const bounds = L.latLngBounds(routeCoords);
          map.fitBounds(bounds, { padding: [50, 50],  maxZoom: 16 });
        `
            : '// No route geometry available'
        }

        true; // Return true to indicate success
      } catch (error) {
        console.error('Error in map script:', error);
        false; // Return false to indicate failure
      }
    `;

    webViewRef.current.injectJavaScript(script);
  }, [ride, routeGeometry, isMapReady]);

  useEffect(() => {
    updateMap();
  }, [updateMap]);

  const getMapHtml = useCallback(() => {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
          <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
          <style>
            body { margin: 0; }
            #map { width: 100%; height: 100vh; }
          </style>
        </head>
        <body>
          <div id="map"></div>
          <script>
            const map = L.map('map', {
              zoomControl: false
            }).setView([${ride.pickup_location.coordinates[1]}, 
          ${ride.pickup_location.coordinates[0]}], 16);
            window.map = map;
            window.markers = [];

            
            L.tileLayer('https://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}&scale=3', {
              maxZoom: 25,
              minZoom: 1,
              subdomains: ['mt0', 'mt1', 'mt2', 'mt3'],
              tileSize: 256,
              updateWhenIdle: true,
              keepBuffer: 2
            }).addTo(map);

            window.initMap = () => {
              window.ReactNativeWebView.postMessage('mapReady');
            };

            map.whenReady(() => {
              window.initMap();
            });
          </script>
        </body>
      </html>
    `;
  }, [ride]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      // Return true to prevent default behavior
      return true;
    });

    return () => backHandler.remove();
  }, []);

  useEffect(() => {
    const storeRideId = async () => {
      try {
        await AsyncStorage.setItem('@active_ride', rideId as string);
      } catch (error) {
        console.error('Error storing ride ID:', error);
      }
    };

    storeRideId();
  }, [rideId]);

  useEffect(() => {
    if (ride?.status === 'COMPLETED' || ride?.status === 'CANCELED') {
      AsyncStorage.removeItem('@active_ride')
        .then(() => {
          router.replace('/(main)');
        })
        .catch(error => console.error('Error clearing ride ID:', error));
    }
  }, [ride?.status]);

  const [state, setState] = useState({
    isModalVisible: false,
    cancelReason: '',
    otherReason: '',
  });

  const handleCancelConfirm = async () => {
    try {
      const finalReason = state.cancelReason === 'Other' ? state.otherReason : state.cancelReason;
      await updateRideStatus({
        variables: {
          rideId,
          status: 'cancelled',
          cancelReason: finalReason,
        },
      });

      clearRouteInfo();
      setDropoffLocation(null);
      await AsyncStorage.removeItem('@active_ride');

      router.replace('/(main)');
    } catch (error) {
      console.error('Error canceling ride:', error);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (error || !ride) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text>Error loading ride details</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.mapContainer}>
        <WebView
          ref={webViewRef}
          source={{ html: getMapHtml() }}
          style={styles.map}
          onMessage={event => {
            if (event.nativeEvent.data === 'mapReady') {
              setIsMapReady(true);
            }
          }}
        />
      </View>

      {/* Status Bar */}
      <Surface style={[styles.statusBar, { backgroundColor: theme.colors.surface }]} elevation={0}>
        <View style={styles.statusContent}>
          <MaterialCommunityIcons name="clock-outline" size={20} color={theme.colors.primary} />
          <Text style={styles.statusText}>Status: {ride?.status}</Text>
        </View>
      </Surface>

      {/* Driver Info Card */}
      <Surface style={[styles.driverCard, { backgroundColor: theme.colors.surface }]} elevation={4}>
        <View style={styles.driverHeader}>
          <View style={styles.driverProfile}>
            <Avatar.Text
              size={50}
              label={ride.driver.name.charAt(0)}
              style={{ backgroundColor: theme.colors.primaryContainer }}
            />
            <View style={styles.driverDetails}>
              <Text variant="titleMedium">{ride.driver.name}</Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurface }}>
                {ride.driver.vehicles[0].color} {ride.driver.vehicles[0].make}{' '}
                {ride.driver.vehicles[0].model}
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.primary }}>
                {ride.driver.vehicles[0].license_plate}
              </Text>
            </View>
          </View>
          <View style={styles.driverActions}>
            <IconButton
              icon="phone"
              mode="contained"
              size={24}
              onPress={() => {
                if (!ride?.driver?.phone_number) {
                  Alert.alert('No Phone Number', 'Driver phone number is not available');
                  return;
                }

                try {
                  Linking.openURL(`tel:${ride.driver.phone_number}`);
                } catch (error) {
                  console.error('Error calling driver:', error);
                  Alert.alert('Error', 'Could not make the call');
                }
              }}
              containerColor={theme.colors.primaryContainer}
              iconColor={theme.colors.onPrimary}
            />
          </View>
        </View>

        <Divider style={{ marginVertical: 12 }} />

        <View style={styles.rideInfo}>
          <View style={styles.locationInfo}>
            <MaterialCommunityIcons name="map-marker" size={20} color={theme.colors.primary} />
            <Text variant="bodyMedium" numberOfLines={1} style={styles.address}>
              {ride.pickup_address}
            </Text>
          </View>
          <View style={styles.locationInfo}>
            <MaterialCommunityIcons name="map-marker" size={20} color={theme.colors.error} />
            <Text variant="bodyMedium" numberOfLines={1} style={styles.address}>
              {ride.dropoff_address}
            </Text>
          </View>
        </View>

        <View style={styles.estimateInfo}>
          <View style={styles.estimate}>
            <Text variant="labelSmall">Distance</Text>
            <Text variant="bodyMedium">{(ride.estimated_distance / 1000).toFixed(1)} km</Text>
          </View>
          <View style={styles.estimate}>
            <Text variant="labelSmall">Duration</Text>
            <Text variant="bodyMedium">{Math.round(ride.estimated_duration)} mins</Text>
          </View>
          <View style={styles.estimate}>
            <Text variant="labelSmall">Fare</Text>
            <Text variant="bodyMedium"> {`Rs. ${ride.estimated_fare}`}</Text>
          </View>
        </View>

        <View style={styles.rideActions}>
          <Button
            mode="outlined"
            icon="close"
            onPress={() => setState(prevState => ({ ...prevState, isModalVisible: true }))}
            style={styles.cancelButton}
          >
            Cancel Ride
          </Button>
        </View>

        <Portal>
          <Modal
            visible={state.isModalVisible}
            onDismiss={() => setState(prevState => ({ ...prevState, isModalVisible: false }))}
            contentContainerStyle={styles.modalContainer}
          >
            <Surface style={styles.modalSurface}>
              <View style={styles.modalHeader}>
                <Title style={styles.modalTitle}>{t('mapView.selectCancellationReason')}</Title>
                <Text style={styles.modalSubtitle}>{t('mapView.cancelReasonDescription')}</Text>
              </View>

              <ScrollView style={styles.reasonsContainer}>
                <RadioButton.Group
                  onValueChange={value =>
                    setState(prevState => ({ ...prevState, cancelReason: value }))
                  }
                  value={state.cancelReason}
                >
                  {[
                    'driverNotMoving',
                    'driverTooFar',
                    'wrongLocation',
                    'driverNotResponding',
                    'longWait',
                    'changedMind',
                    'foundAnotherRide',
                    'priceTooHigh',
                    'safetyConcerns',
                    'bookedByMistake',
                    'other',
                  ].map(reason => (
                    <RadioButton.Item
                      key={reason}
                      label={t(`common.cancelReasons.${reason}`)}
                      value={reason}
                      style={styles.radioItem}
                      labelStyle={styles.radioLabel}
                    />
                  ))}
                </RadioButton.Group>

                {state.cancelReason === 'other' && (
                  <TextInput
                    mode="outlined"
                    label={t('common.cancelReasons.pleaseSpecify')}
                    value={state.otherReason}
                    onChangeText={text =>
                      setState(prevState => ({ ...prevState, otherReason: text }))
                    }
                    multiline
                    numberOfLines={3}
                    style={styles.otherReasonInput}
                  />
                )}
              </ScrollView>

              <View style={styles.modalActions}>
                <Button
                  mode="outlined"
                  onPress={() =>
                    setState(prevState => ({
                      ...prevState,
                      isModalVisible: false,
                      cancelReason: '',
                      otherReason: '',
                    }))
                  }
                  style={styles.actionButton}
                >
                  {t('common.cancel')}
                </Button>
                <Button
                  mode="contained"
                  onPress={handleCancelConfirm}
                  buttonColor={theme.colors.error}
                  style={styles.actionButton}
                  disabled={
                    !state.cancelReason || (state.cancelReason === 'other' && !state.otherReason)
                  }
                >
                  {t('common.confirm')}
                </Button>
              </View>
            </Surface>
          </Modal>
        </Portal>
      </Surface>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapContainer: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  statusBar: {
    position: 'absolute',
    top: 16,
    left: 16,
    right: 16,
    // backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    padding: 12,
  },
  statusContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  statusText: {
    marginLeft: 8,
    fontWeight: '500',
  },
  driverCard: {
    // position: 'absolute',
    // bottom: 0,
    // left: 0,
    // right: 0,
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 20,
    paddingBottom: 40,
  },
  driverHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  driverProfile: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  driverDetails: {
    marginLeft: 12,
  },
  driverActions: {
    flexDirection: 'row',
  },
  rideInfo: {
    marginBottom: 16,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
  },
  address: {
    marginLeft: 8,
    flex: 1,
  },
  estimateInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  estimate: {
    alignItems: 'center',
  },
  modalContainer: {
    margin: 20,
    backgroundColor: 'transparent',
  },
  modalSurface: {
    // backgroundColor: theme.colors.surface,
    borderRadius: 28,
    padding: 24,
    elevation: 5,
    maxHeight: '80%',
  },
  modalHeader: {
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    // color: theme.colors.onSurface,
    marginBottom: 8,
  },
  modalSubtitle: {
    fontSize: 16,
    // color: theme.colors.onSurfaceVariant,
    lineHeight: 22,
  },
  reasonsContainer: {
    maxHeight: '60%',
  },
  radioItem: {
    paddingVertical: 8,
    marginVertical: 2,
    borderRadius: 8,
  },
  radioLabel: {
    fontSize: 16,
    // color: theme.colors.onSurface,
  },
  otherReasonInput: {
    marginTop: 16,
    marginBottom: 8,
    backgroundColor: 'transparent',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
    marginTop: 24,
    paddingTop: 16,
    borderTopWidth: 1,
    // borderTopColor: theme.colors.outlineVariant,
  },
  actionButton: {
    minWidth: 100,
  },
  rideActions: {
    marginTop: 16,
  },
  cancelButton: {
    marginTop: 8,
  },
});

export default RideTracking;
