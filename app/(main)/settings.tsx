import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, Alert, Linking, Platform } from 'react-native';
import { List, Text, Surface, IconButton, Divider, Button, Switch } from 'react-native-paper';
import { router } from 'expo-router';
import { useSignOut } from '@nhost/react';
import useLocationServices from '../hooks/useLocationServices';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme } from '../../src/theme/ThemeContext';
import { useApolloClient } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import LanguageSelector from '../components/LanguageSelector';
import { useNotifications } from 'app/contexts/NotificationContext';
import Constants from 'expo-constants';
import { LINKS } from '@/constants';

export default function SettingsScreen() {
  const { t } = useTranslation();
  const { signOut, isSuccess } = useSignOut();
  const { stopForegroundTracking } = useLocationServices();
  const { isDarkMode, toggleTheme, theme } = useTheme();
  const apolloClient = useApolloClient();
  const { isNotificationsEnabled, disableNotifications } = useNotifications();

  const handleSignOut = async () => {
    try {
      if (isNotificationsEnabled) {
        await disableNotifications();
      }

      // Clear all storage
      await AsyncStorage.clear();

      // Stop location tracking
      stopForegroundTracking();

      // Clear Apollo cache
      await apolloClient.resetStore();

      // Sign out from Nhost
      const { error } = await signOut();

      if (error) {
        console.error('Log out error:', error);
        return;
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const confirmSignOut = () => {
    Alert.alert(t('auth.logout'), t('auth.confirmLogout'), [
      {
        text: t('common.cancel'),
        style: 'cancel',
      },
      {
        text: t('auth.logout'),
        style: 'destructive',
        onPress: handleSignOut,
      },
    ]);
  };

  return (
    <>
      <ScrollView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
        contentContainerStyle={styles.scrollContent}
        stickyHeaderIndices={[0]}
      >
        <View style={styles.header}>
          <Surface
            style={[styles.headerCard, { backgroundColor: theme.colors.surface }]}
            elevation={4}
          >
            <View style={styles.headerLeft}>
              <IconButton
                icon="arrow-left"
                size={24}
                iconColor={theme.colors.primary}
                onPress={() => router.back()}
              />
              <Text
                variant="titleLarge"
                style={[styles.headerTitle, { color: theme.colors.onSurface }]}
              >
                {t('settings.title')}
              </Text>
            </View>
          </Surface>
        </View>

        <Surface
          style={[styles.settingsCard, { backgroundColor: theme.colors.surface }]}
          elevation={2}
        >
          <List.Section>
            <List.Subheader
              style={[styles.sectionHeader, { color: theme.colors.onSurfaceVariant }]}
            >
              {t('settings.appearance')}
            </List.Subheader>
            <List.Item
              title={t('settings.darkMode')}
              titleStyle={{ color: theme.colors.onSurface }}
              left={props => (
                <List.Icon
                  {...props}
                  icon={isDarkMode ? 'weather-night' : 'weather-sunny'}
                  color={theme.colors.primary}
                />
              )}
              right={props => (
                <Switch
                  {...props}
                  value={isDarkMode}
                  onValueChange={toggleTheme}
                  color={theme.colors.primary}
                />
              )}
            />
          </List.Section>

          <List.Section>
            <List.Subheader
              style={[styles.sectionHeader, { color: theme.colors.onSurfaceVariant }]}
            >
              {t('common.account')}
            </List.Subheader>
            <List.Item
              title={t('settings.profile')}
              left={props => <List.Icon {...props} icon="account" color={theme.colors.primary} />}
              right={props => (
                <List.Icon {...props} icon="chevron-right" color={theme.colors.onSurface} />
              )}
              onPress={() => router.push('/(main)/settings/profile')}
              titleStyle={{ color: theme.colors.onSurface }}
            />
            <List.Item
              title={t('settings.rideHistory.title')}
              left={props => <List.Icon {...props} icon="history" color={theme.colors.primary} />}
              right={props => (
                <List.Icon {...props} icon="chevron-right" color={theme.colors.onSurface} />
              )}
              onPress={() => router.push('/(main)/settings/ride-history')}
              titleStyle={{ color: theme.colors.onSurface }}
            />
            <List.Item
              title={t('settings.notifications')}
              left={props => <List.Icon {...props} icon="bell" color={theme.colors.primary} />}
              right={props => (
                <List.Icon {...props} icon="chevron-right" color={theme.colors.onSurface} />
              )}
              onPress={() => router.push('/(main)/settings/notifications')}
              titleStyle={{ color: theme.colors.onSurface }}
            />
          </List.Section>

          <List.Section>
            <List.Subheader
              style={[styles.sectionHeader, { color: theme.colors.onSurfaceVariant }]}
            >
              {t('common.support')}
            </List.Subheader>
            <List.Item
              title={t('settings.contactSupport')}
              description={t('settings.supportDescription')}
              left={props => <List.Icon {...props} icon="headset" color={theme.colors.primary} />}
              right={props => (
                <List.Icon {...props} icon="chevron-right" color={theme.colors.onSurface} />
              )}
              onPress={() => router.push('/(main)/settings/support')}
              titleStyle={{ color: theme.colors.onSurface }}
              descriptionStyle={{ color: theme.colors.onSurface, opacity: 0.6 }}
            />
          </List.Section>

          <List.Section>
            <List.Subheader
              style={[styles.sectionHeader, { color: theme.colors.onSurfaceVariant }]}
            >
              {t('common.other')}
            </List.Subheader>
            <List.Item
              title={t('settings.rateApp')}
              description={t('settings.rateDescription')}
              left={props => <List.Icon {...props} icon="star" color={theme.colors.primary} />}
              right={props => (
                <List.Icon {...props} icon="chevron-right" color={theme.colors.onSurface} />
              )}
              onPress={() => {
                const storeUrl = Platform.OS === 'ios' ? LINKS.iosApp : LINKS.passengerApp;
                Linking.openURL(storeUrl).catch(err => console.error('Error opening store:', err));
              }}
              titleStyle={{ color: theme.colors.onSurface }}
              descriptionStyle={{ color: theme.colors.onSurface, opacity: 0.6 }}
            />
          </List.Section>
        </Surface>

        <LanguageSelector />

        <List.Item
          title={t('auth.logout')}
          titleStyle={{ color: theme.colors.onSurface }}
          onPress={confirmSignOut}
          left={props => (
            <List.Icon {...props} icon="logout" color={theme.colors.onSurfaceDisabled} />
          )}
        />
      </ScrollView>
      <View style={[styles.footer, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.footerText, { color: theme.colors.onSurfaceVariant }]}>
          Made in Jhapa with <Text style={{ color: theme.colors.error }}>♥</Text>{' '}
          <Text style={[styles.versionText, { color: theme.colors.onSurfaceVariant }]}>
            (Version {Constants.expoConfig?.version || '1.0.0'})
          </Text>
        </Text>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  headerCard: {
    padding: 8,
    borderRadius: 12,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    marginLeft: 8,
    fontWeight: '600',
  },
  settingsCard: {
    borderRadius: 12,
  },
  sectionHeader: {
    fontWeight: '600',
  },
  footer: {
    paddingBottom: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  footerText: {
    fontSize: 12,
    opacity: 0.8,
  },
  versionText: {
    fontSize: 11,
    opacity: 0.6,
    marginTop: 4,
  },
});
