import { gql } from '@apollo/client';

export const GET_USER_PROFILE = gql`
  query GetUserProfile($userId: uuid!) {
    user_profiles(where: { user_id: { _eq: $userId } }) {
      id
      full_name
      phone
      avatar_url
      metadata
      is_active
      last_seen_at
      dob
      gender
      blood_group
      blood_donation_consent
    }
  }
`;

export const CREATE_USER_PROFILE = gql`
  mutation CreateUserProfile(
    $userId: uuid!
    $fullName: String!
    $phone: String!
    $avatarUrl: String
    $dob: String!
    $gender: String!
    $location: geography
    $metadata: jsonb
    $bloodGroup: blood_group_enum
    $bloodDonationConsent: Boolean
  ) {
    insert_user_profiles_one(
      object: {
        user_id: $userId
        full_name: $fullName
        phone: $phone
        avatar_url: $avatarUrl
        dob: $dob
        gender: $gender
        location: $location
        metadata: $metadata
        is_active: true
        blood_group: $bloodGroup
        blood_donation_consent: $bloodDonationConsent
      }
    ) {
      id
    }
  }
`;

export const UPDATE_USER_PROFILE = gql`
  mutation UpdateUserProfile(
    $profileId: uuid!
    $fullName: String!
    $phone: String!
    $avatarUrl: String
    $dob: date
    $gender: String!
    $location: geography
    $metadata: jsonb
    $bloodGroup: blood_group_enum
    $bloodDonationConsent: Boolean
  ) {
    update_user_profiles_by_pk(
      pk_columns: { id: $profileId }
      _set: {
        full_name: $fullName
        phone: $phone
        avatar_url: $avatarUrl
        dob: $dob
        gender: $gender
        location: $location
        metadata: $metadata
        blood_group: $bloodGroup
        blood_donation_consent: $bloodDonationConsent
      }
    ) {
      id
    }
  }
`;

const passengerQueries = {
  GET_USER_PROFILE,
  CREATE_USER_PROFILE,
  UPDATE_USER_PROFILE,
};

export default passengerQueries;
