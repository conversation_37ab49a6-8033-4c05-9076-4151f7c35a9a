import { gql } from '@apollo/client';

export const GET_DRIVER = gql`
  query GetDriver($userId: uuid!) {
    drivers(where: { user_id: { _eq: $userId } }) {
      id
      license_number
      verification_status
      is_active
      total_rides
      name
      phone_number
      vehicles {
        id
        vehicle_type
        make
        model
        year
        color
        license_plate
      }
    }
  }
`;

export const GET_DRIVER_PUSH_TOKEN = gql`
  query GetDriver($driverId: uuid!) {
    drivers_by_pk(id: $driverId) {
      notification_token
    }
  }
`;

export const CHECK_EXISTING_DRIVER = gql`
  query CheckExistingDriver($userId: uuid!) {
    drivers_aggregate(where: { user_id: { _eq: $userId } }) {
      aggregate {
        count
      }
    }
  }
`;

export const CREATE_DRIVER = gql`
  mutation CreateDriver(
    $userId: uuid!
    $license_number: String!
    $name: String!
    $phone_number: String!
  ) {
    insert_drivers_one(
      object: {
        user_id: $userId
        license_number: $license_number
        name: $name
        phone_number: $phone_number
      }
    ) {
      id
    }
  }
`;

export const UPDATE_DRIVER = gql`
  mutation UpdateDriver($driverId: uuid!, $data: drivers_set_input!) {
    update_drivers_by_pk(pk_columns: { id: $driverId }, _set: $data) {
      id
      verification_status
    }
  }
`;

export const CREATE_VEHICLE = gql`
  mutation CreateVehicle(
    $driverId: uuid!
    $vehicleType: vehicle_type_enum!
    $make: String!
    $model: String!
    $year: String!
    $color: String!
    $licensePlate: String!
  ) {
    insert_vehicles_one(
      object: {
        driver_id: $driverId
        vehicle_type: $vehicleType
        make: $make
        model: $model
        year: $year
        color: $color
        license_plate: $licensePlate
        is_active: true
      }
    ) {
      id
    }
  }
`;

export const UPDATE_VEHICLE = gql`
  mutation UpdateVehicle(
    $vehicleId: uuid!
    $vehicleType: vehicle_type_enum!
    $make: String!
    $model: String!
    $year: String!
    $color: String!
    $licensePlate: String!
  ) {
    update_vehicles_by_pk(
      pk_columns: { id: $vehicleId }
      _set: {
        vehicle_type: $vehicleType
        make: $make
        model: $model
        year: $year
        color: $color
        license_plate: $licensePlate
      }
    ) {
      id
    }
  }
`;

export const UPDATE_DRIVER_LOCATION = gql`
  mutation UpdateDriverLocation(
    $driverId: uuid!
    $location: geography!
    $heading: numeric
    $speed: numeric
    $accuracy: numeric
    $batteryLevel: Int
  ) {
    update_driver_locations_current(
      where: { driver_id: { _eq: $driverId } }
      _set: {
        location: $location
        heading: $heading
        speed: $speed
        accuracy: $accuracy
        battery_level: $batteryLevel
      }
    ) {
      affected_rows
      returning {
        id
        driver_id
      }
    }
  }
`;

const driverQueries = {
  GET_DRIVER,
  CHECK_EXISTING_DRIVER,
  CREATE_DRIVER,
  UPDATE_DRIVER,
  CREATE_VEHICLE,
  UPDATE_VEHICLE,
  UPDATE_DRIVER_LOCATION,
  GET_DRIVER_PUSH_TOKEN,
};

export default driverQueries;
