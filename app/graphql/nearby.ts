import { gql } from '@apollo/client';

export const CREATE_RIDE = gql`
  mutation CreateRide($input: rides_insert_input!) {
    insert_rides_one(object: $input) {
      id
      status
      driver {
        id
        name
        phone_number
        vehicles {
          id
          model
        }
      }
    }
  }
`;

export const UPDATE_USER_PROFILE = gql`
  mutation UpdateUserProfile($id: uuid!, $data: user_profiles_set_input!) {
    update_user_profiles_by_pk(pk_columns: { id: $id }, _set: $data) {
      id
    }
  }
`;

export const CREATE_FREE_RIDE_REQUEST = gql`
  mutation CreateFreeRideRequest($input: free_rides_insert_input!) {
    insert_free_rides_one(object: $input) {
      id
      driver {
        id
        notification_token
      }
    }
  }
`;

export const GET_ACTIVE_FREE_RIDE_REQUESTS = gql`
  query GetFreeRideRequests($profileId: uuid!) {
    free_rides(where: { profile_id: { _eq: $profileId }, is_active: { _eq: true } }) {
      id
      user_profile {
        id
        phone
        location
        full_name
      }
      driver {
        id
        driver_locations_current {
          id
        }
        name
        phone_number
      }
    }
  }
`;

export const SUBSCRIBE_TO_ACTIVE_FREE_RIDE_REQUESTS = gql`
  subscription SubscribeToActiveFreeRideRequests($profileId: uuid!) {
    free_rides(where: { profile_id: { _eq: $profileId }, is_active: { _eq: true } }) {
      id
      status
      created_at
      user_profile {
        id
        phone
        # location
        full_name
      }
      pickup_location
      driver {
        id
        is_active
        name
        phone_number
        local_unit
        user {
          id
          displayName
        }
        vehicles {
          id
          license_plate
          model
          color
          make
          vehicle_type
        }
        average_rating
        ratings_aggregate {
          aggregate {
            avg {
              rating
            }
          }
        }

        completedRides: free_rides_aggregate(where: { status: { _eq: "completed" } }) {
          aggregate {
            count
          }
        }
        totalRides: free_rides_aggregate {
          aggregate {
            count
          }
        }
        driver_locations_current {
          id
          driver_id
          location
          heading
          speed
          battery_level
          updated_at
          driver {
            id
            # is_active
            # name
            # phone_number
            # user {
            #   id
            #   displayName
            # }
            # vehicles {
            #   id
            #   license_plate
            #   model
            #   color
            #   make
            #   vehicle_type
            # }
            # average_rating
            # ratings_aggregate {
            #   aggregate {
            #     avg {
            #       rating
            #     }
            #   }
            # }

            # completedRides: free_rides_aggregate(where: { status: { _eq: "completed" } }) {
            #   aggregate {
            #     count
            #   }
            # }
            # totalRides: free_rides_aggregate {
            #   aggregate {
            #     count
            #   }
            # }
          }
        }
        name
        phone_number
      }
    }
  }
`;

export const UPDATE_FREE_RIDE_REQUEST = gql`
  mutation updateFreeRideRequest($id: uuid!, $data: free_rides_set_input!) {
    update_free_rides_by_pk(pk_columns: { id: $id }, _set: $data) {
      id
      driver {
        id
        notification_token
      }
    }
  }
`;

export default {};
