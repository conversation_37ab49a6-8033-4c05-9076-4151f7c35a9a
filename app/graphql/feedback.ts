import { gql } from '@apollo/client';

export const CREATE_FEEDBACK = gql`
  mutation CreateFeedback($object: feedback_insert_input!) {
    insert_feedback_one(object: $object) {
      id
      category
      message
      status
      created_at
    }
  }
`;

export const GET_USER_FEEDBACK = gql`
  query GetUserFeedback($user_id: uuid!) {
    feedback(where: { user_id: { _eq: $user_id } }, order_by: { created_at: desc }) {
      id
      category
      message
      status
      created_at
    }
  }
`;

const feedbackQueries = {
  CREATE_FEEDBACK,
  GET_USER_FEEDBACK,
};

export default feedbackQueries;
