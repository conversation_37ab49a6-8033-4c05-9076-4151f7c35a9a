import { gql } from '@apollo/client';

export const GET_FREE_RIDE_PK = gql`
  query GetFreeRideRequestByPk($id: uuid!) {
    free_rides_by_pk(id: $id) {
      id
      pickup_address
      dropoff_address
      dropoff_location
      pickup_location
      driver_id
      profile_id
      cancel_reason
      status
    }
  }
`;

export const GET_RIDE_HISTORY = gql`
  query GetRideHistory($profileId: uuid!, $offset: Int!, $limit: Int!) {
    free_rides(
      where: {
        profile_id: { _eq: $profileId }
        status: { _in: ["pending", "accepted", "completed", "cancelled"] }
      }
      order_by: { created_at: desc }
      offset: $offset
      limit: $limit
    ) {
      id
      status
      created_at
      pickup_address
      dropoff_address
      cancel_reason
      user_profile {
        full_name
        phone
      }
      driver {
        id
        name
        phone_number
        vehicle {
          id
          color
          make
          model
          vehicle_type
          license_plate
        }
      }
      # free_rides_aggregate(where: { profile_id: { _eq: $profileId } }) {
      #   aggregate {
      #     count
      #   }
      # }
    }
  }
`;

export const GET_FREE_RIDE_REQUESTS = gql`
  query GetFreeRideRequests($profileId: uuid!, $offset: Int!, $limit: Int!) {
    free_rides(
      where: { profile_id: { _eq: $profileId } }
      order_by: { updated_at: desc }
      offset: $offset
      limit: $limit
    ) {
      id
      user_profile {
        id
        full_name
      }
      pickup_address
      dropoff_address
      driver {
        id
        name
        phone_number
        vehicles {
          id
          color
          make
          model
          vehicle_type
          license_plate
        }
      }
      status
      cancel_reason
      updated_at
    }
  }
`;

export default {};
