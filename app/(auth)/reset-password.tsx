import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, Image, Alert } from 'react-native';
import { Text, TextInput, Button, Surface, HelperText } from 'react-native-paper';
import { useResetPassword } from '@nhost/react';
import nhost from 'app/utils/nhost';
import { router } from 'expo-router';
import { COLORS } from '@/constants';
import { useRoute } from '@react-navigation/native';

const RESEND_TIMEOUT = 30; // 30 seconds timeout

const ResetPassword = () => {
  const [email, setEmail] = useState('');
  const [countdown, setCountdown] = useState(0);
  const { resetPassword, isLoading, isSent, isError, error: resetError } = useResetPassword();
  const route = useRoute();

  useEffect(() => {
    let timer: string | number | NodeJS.Timeout | undefined;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [countdown]);

  useEffect(() => {
    if (isSent) {
      setCountdown(RESEND_TIMEOUT);
      Alert.alert('Success', 'Reset link sent! Please check your email.');
    }
  }, [isSent]);

  const handleSubmit = async () => {
    if (!email || !email.includes('@')) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    try {
      await resetPassword(
        email, 
        { 
          redirectTo: 'totoloco://reset-password' 
        }
      );
    } catch (error) {
      console.error('Error sending reset link:', error);
    }
  };



  useEffect(() => {
    const { refreshToken }: any = route.params;
    if (refreshToken) {
      nhost.auth.refreshSession(refreshToken);
    }
  }, [route.params]);

  return (
    <View style={styles.container}>
      <ScrollView 
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
      >
        <Surface style={styles.card} elevation={2}>
          <View style={styles.logoContainer}>
            <Image 
              source={require('../../assets/images/splash-icon.png')}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>

          <Text variant="headlineMedium" style={styles.title}>Login without password</Text>
          <Text variant="bodyLarge" style={styles.subtitle}>Enter your email to receive a login link</Text>

          <View style={styles.form}>
            <TextInput
              mode="outlined"
              label="Email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              style={styles.input}
            />

            {isError && (
              <HelperText type="error" visible={true}>
                {resetError?.message || 'An error occurred while sending reset link'}
              </HelperText>
            )}

            <Button
              mode="contained"
              onPress={handleSubmit}
              loading={isLoading}
              disabled={isLoading || countdown > 0}
              style={styles.button}
              buttonColor={COLORS.primary}
              contentStyle={styles.buttonContent}
            >
              {countdown > 0 ? `Resend in ${countdown}s` : 'Send Reset Link'}
            </Button>

            <Button
              mode="text"
              onPress={() => router.push('/(auth)/login')}
              style={styles.backButton}
            >
              Back to Login
            </Button>
          </View>
        </Surface>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  card: {
    padding: 24,
    borderRadius: 12,
    backgroundColor: COLORS.surface,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  logo: {
    width: 120,
    height: 120,
  },
  title: {
    color: COLORS.primary,
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  subtitle: {
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: 32,
    opacity: 0.7,
  },
  form: {
    gap: 16,
  },
  input: {
    backgroundColor: COLORS.surface,
  },
  button: {
    marginTop: 8,
  },
  buttonContent: {
    paddingVertical: 8,
  },
  backButton: {
    marginTop: 16,
  }
});

export default ResetPassword;
