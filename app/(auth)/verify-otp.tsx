import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, View, ScrollView, Image, Alert, Dimensions } from 'react-native';
import { Text, Button, Surface } from 'react-native-paper';
import { router, useLocalSearchParams } from 'expo-router';
import { COLORS } from '@/constants';
import OTPTextInput from 'react-native-otp-textinput';
import { useSignInSmsPasswordless } from '@nhost/react';

// const { width } = Dimensions.get('window');
// const CARD_WIDTH = Math.min(width - 48, 400);
const RESEND_TIMEOUT = 30; // seconds

export default function VerifyOTPScreen() {
  const [otp, setOtp] = useState('');
  const [timeLeft, setTimeLeft] = useState(RESEND_TIMEOUT);
  const [canResend, setCanResend] = useState(false);
  const otpInput = useRef(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const { phoneNumber } = useLocalSearchParams();

  const {
    signInSmsPasswordless,
    sendOtp,
    isLoading,
    isSuccess,
    error: errorLogin,
    isError,
  } = useSignInSmsPasswordless();

  useEffect(() => {
    startResendTimer();
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current as NodeJS.Timeout);
      }
    };
  }, []);

  const startResendTimer = () => {
    setCanResend(false);
    setTimeLeft(RESEND_TIMEOUT);

    if (timerRef.current) {
      clearInterval(timerRef.current as NodeJS.Timeout);
    }

    timerRef.current = setInterval(() => {
      setTimeLeft(prevTime => {
        if (prevTime <= 1) {
          if (timerRef.current) {
            clearInterval(timerRef.current as NodeJS.Timeout);
          }
          setCanResend(true);
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);
  };

  const handleVerifyOTP = async () => {
    if (otp.length !== 6) {
      Alert.alert('Error', 'Please enter a valid 6-digit OTP');
      return;
    }

    try {
      await sendOtp(String(phoneNumber), otp);
    } catch (error) {
      Alert.alert('Error', 'Failed to verify OTP. Please try again.');
    }
  };

  const handleResendOTP = async () => {
    if (!canResend) return;

    try {
      await signInSmsPasswordless(String(phoneNumber));
      Alert.alert('Success', `New OTP has been sent to your phone number ${phoneNumber}`);
      startResendTimer();
    } catch (error) {
      Alert.alert('Error', 'Failed to resend OTP. Please try again.');
    }
  };

  const formatTime = (seconds: number) => {
    return `${String(Math.floor(seconds / 60)).padStart(2, '0')}:${String(seconds % 60).padStart(2, '0')}`;
  };

  return (
    <ScrollView contentContainerStyle={styles.container} keyboardShouldPersistTaps="handled">
      <Surface style={styles.card} elevation={2}>
        <View style={styles.logoContainer}>
          <Image
            source={require('../../assets/images/splash-icon.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        <Text variant="headlineMedium" style={styles.title}>
          Verify Phone Number
        </Text>
        <Text variant="bodyLarge" style={styles.subtitle}>
          Enter the 6-digit code sent to {phoneNumber}
        </Text>

        <View style={styles.form}>
          <OTPTextInput
            ref={otpInput}
            handleTextChange={setOtp}
            inputCount={6}
            keyboardType="numeric"
            tintColor={COLORS.primary}
            offTintColor={COLORS.secondary}
            textInputStyle={styles.otpInput}
            containerStyle={styles.otpContainer}
          />

          <Button
            mode="contained"
            onPress={handleVerifyOTP}
            loading={isLoading}
            disabled={isLoading}
            style={styles.button}
            contentStyle={styles.buttonContent}
          >
            Verify
          </Button>

          <Button
            mode="text"
            onPress={handleResendOTP}
            disabled={!canResend || isLoading}
            style={styles.resendButton}
          >
            {canResend ? 'Resend Code' : `Resend Code in ${formatTime(timeLeft)}`}
          </Button>
        </View>
      </Surface>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  card: {
    width: '100%',
    backgroundColor: COLORS.surface,
    borderRadius: 24,
    padding: 24,
    alignItems: 'center',
  },
  logoContainer: {
    marginBottom: 32,
  },
  logo: {
    width: 120,
    height: 120,
  },
  title: {
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: 32,
    opacity: 0.7,
  },
  form: {
    width: '100%',
    alignItems: 'center',
    gap: 8,
  },
  otpContainer: {
    marginBottom: 16,
  },
  otpInput: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.secondary,
    color: COLORS.text,
    width: 40,
    height: 55,
    fontSize: 24,
  },
  button: {
    marginTop: 16,
    borderRadius: 12,
    width: '100%',
  },
  buttonContent: {
    paddingVertical: 8,
  },
  resendButton: {
    marginTop: 8,
  },
});
