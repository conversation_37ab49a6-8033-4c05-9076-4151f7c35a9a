import React, { useEffect, useState } from 'react';
import {
  StyleSheet,
  View,
  ScrollView,
  Image,
  Dimensions,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Platform,
} from 'react-native';
import { Text, Button, Surface, TextInput, useTheme } from 'react-native-paper';
import * as WebBrowser from 'expo-web-browser';
import AsyncStorage from '@react-native-async-storage/async-storage';
import nhost from 'app/utils/nhost';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import {
  handleCallSupport,
  handleEmailSupport,
  handleWhatsAppSupport,
} from 'app/utils/social-support';
import { useRouter } from 'expo-router';
import { useIsFocused } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';

const { width, height } = Dimensions.get('window');
const CARD_WIDTH = Math.min(width - 48, 400);

const LoginScreen = () => {
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [hidePassword, setHidePassword] = useState(true);
  const [isEmailLogin, setIsEmailLogin] = useState(false);
  const theme = useTheme();
  const router = useRouter();
  const isFocused = useIsFocused();
  const { t } = useTranslation();

  const handleGoogleLogin = async () => {
    try {
      setLoading(true);
      const redirectUri = 'totoloco://login';
      const { providerUrl, error } = await nhost.auth.signIn({
        provider: 'google',
        options: {
          redirectTo: redirectUri,
          allowedRoles: ['user', 'me'],
          defaultRole: 'user',
        },
      });

      if (error) {
        Alert.alert('Error', 'something went wrong');
        // console.error('Error getting auth URL:', error);
        return;
      }

      const result = await WebBrowser.openAuthSessionAsync(providerUrl as string, redirectUri);

      if (result.type === 'success' && result.url) {
        const urlParams = new URLSearchParams(result.url.split('?')[1]);
        const refreshToken = urlParams.get('refreshToken');

        if (refreshToken) {
          await AsyncStorage.setItem('refreshToken', refreshToken);
          await nhost.auth.refreshSession(refreshToken);
        } else {
          Alert.alert('Error', 'something went wrong');
          // console.error('Failed to retrieve refresh token');
        }
      }
    } catch (error) {
      // console.error('Error during Google OAuth:', error);
      Alert.alert('Error', 'something went wrong');
    } finally {
      setLoading(false);
    }
  };

  const handleEmailLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }

    try {
      setLoading(true);
      const data = await nhost.auth.signIn({
        email,
        password,
        options: {
          allowedRoles: ['user', 'me'],
          defaultRole: 'user',
        },
      });

      if (data.error) {
        Alert.alert('Error', data.error.message);
        return;
      }
    } catch (error) {
      // console.error('Error during email login:', error);
      Alert.alert('Error', 'Failed to sign in. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Dismiss browser sessions when the screen is focused
  useEffect(() => {
    const clearBrowserSessions = async () => {
      if (isFocused) {
        await new Promise(resolve => setTimeout(resolve, 500));

        if (Platform.OS === 'ios') {
          try {
            await WebBrowser.dismissBrowser();
            await WebBrowser.dismissAuthSession();
          } catch (error) {
            // Silently handle any dismissal errors
          }
        } else {
          // For Android, only dismiss auth session
          try {
            await WebBrowser.dismissAuthSession();
          } catch (error) {
            // Silently handle any dismissal errors
          }
        }
      }
    };
    clearBrowserSessions();
  }, [isFocused]);

  const handleAppleLogin = async () => {
    try {
      setLoading(true);

      // Dismiss any active sessions and prepare the browser
      // await WebBrowser.dismissBrowser();
      // await WebBrowser.dismissAuthSession();
      await WebBrowser.warmUpAsync();

      // Define the redirect URI (ensure it matches your app scheme configuration)
      const redirectUri = 'totoloco://login';

      // Request the provider URL from nhost for Apple sign in
      const { providerUrl, error } = await nhost.auth.signIn({
        provider: 'apple',
        options: {
          redirectTo: redirectUri,
          allowedRoles: ['user', 'me'],
          defaultRole: 'user',
        },
      });

      if (error) {
        Alert.alert('Login Error', error.message || 'Something went wrong.');
        return;
      }
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Open the authentication session
      const result = await WebBrowser.openAuthSessionAsync(providerUrl as string, redirectUri, {
        preferEphemeralSession: true,
      });

      if (result.type === 'success') {
        // Extract query parameters from the result URL
        const queryString = result?.url?.split('?')[1];
        const urlParams = new URLSearchParams(queryString);
        const refreshToken = urlParams.get('refreshToken');

        if (refreshToken) {
          // Store the refresh token locally
          await AsyncStorage.setItem('refreshToken', refreshToken);
          // Refresh the nhost session using the retrieved token
          await nhost.auth.refreshSession(refreshToken);
        } else {
          Alert.alert('Login Error', 'Failed to retrieve refresh token.');
        }
      } else if (result.type === 'dismiss') {
        Alert.alert('Login Canceled', 'The authentication session was dismissed.');
      }

      {
        Platform.OS === 'ios' && (await WebBrowser.dismissBrowser());
      }
      await WebBrowser.dismissAuthSession();
    } catch (err) {
      console.error(err);
      Alert.alert('Login Error', 'Something went wrong during login.');
    } finally {
      setLoading(false);
    }
  };

  return (
    // <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.colors.background }]}>
    <ScrollView
      contentContainerStyle={[styles.container, { backgroundColor: theme.colors.background }]}
    >
      <Surface style={[styles.card, { backgroundColor: theme.colors.surface }]} elevation={2}>
        <View style={styles.logoContainer}>
          <Image
            source={require('../../assets/images/bolaoo-logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        <View style={styles.textContainer}>
          <Text variant="headlineMedium" style={[styles.title, { color: theme.colors.onSurface }]}>
            Welcome to{' '}
            <Text
              variant="headlineMedium"
              style={[styles.titleHighlight, { color: theme.colors.primary }]}
            >
              Bolaoo Rides
            </Text>
          </Text>
          <Text
            variant="bodyLarge"
            style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}
          >
            Bolaoo, right now!!
          </Text>
        </View>

        {!isEmailLogin ? (
          <>
            <Button
              mode="contained"
              style={[styles.loginButton, { borderColor: theme.colors.outline }]}
              contentStyle={styles.buttonContent}
              icon={() => (
                <MaterialCommunityIcons
                  name="google"
                  size={24}
                  color={theme.colors.onPrimary}
                  style={styles.googleIcon}
                />
              )}
              buttonColor={theme.colors.primary}
              textColor={theme.colors.onPrimary}
              onPress={handleGoogleLogin}
              disabled={loading}
              loading={loading}
            >
              Continue with Google
            </Button>

            {Platform.OS === 'ios' && (
              <>
                <View style={styles.divider}>
                  <View
                    style={[styles.dividerLine, { backgroundColor: theme.colors.outlineVariant }]}
                  />
                  <Text style={[styles.dividerText, { color: theme.colors.onSurfaceVariant }]}>
                    or
                  </Text>
                  <View
                    style={[styles.dividerLine, { backgroundColor: theme.colors.outlineVariant }]}
                  />
                </View>
                <Button
                  mode="contained"
                  style={[
                    styles.loginButton,
                    { backgroundColor: '#000000', borderColor: theme.colors.outline },
                  ]}
                  contentStyle={styles.buttonContent}
                  icon={() => (
                    <MaterialCommunityIcons
                      name="apple"
                      size={24}
                      color="#FFFFFF"
                      style={styles.googleIcon}
                    />
                  )}
                  onPress={handleAppleLogin}
                  disabled={loading}
                  loading={loading}
                  textColor="#FFFFFF"
                >
                  Continue with Apple
                </Button>
              </>
            )}

            {/* <View style={styles.troubleLoginContainer}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                {t('auth.troubleLogin')}
              </Text>
              <Button
                mode="text"
                onPress={() => setIsEmailLogin(true)}
                style={{ marginLeft: -8 }}
                labelStyle={{ color: theme.colors.primary }}
              >
                {t('auth.useEmail')}
              </Button>
            </View> */}
          </>
        ) : (
          <View style={styles.emailLoginContainer}>
            <TextInput
              label="Email"
              value={email}
              onChangeText={setEmail}
              mode="outlined"
              style={styles.input}
              keyboardType="email-address"
              autoCapitalize="none"
              left={<TextInput.Icon icon="email" />}
            />
            <TextInput
              label="Password"
              value={password}
              onChangeText={setPassword}
              mode="outlined"
              style={styles.input}
              secureTextEntry={hidePassword}
              left={<TextInput.Icon icon="lock" />}
              right={
                <TextInput.Icon
                  icon={hidePassword ? 'eye-off' : 'eye'}
                  onPress={() => setHidePassword(!hidePassword)}
                />
              }
            />
            <Button
              mode="contained"
              style={styles.loginButton}
              contentStyle={styles.buttonContent}
              onPress={handleEmailLogin}
              disabled={loading}
              loading={loading}
            >
              Sign In
            </Button>
            <Button
              mode="text"
              onPress={() => setIsEmailLogin(false)}
              style={styles.switchButton}
              labelStyle={{ color: theme.colors.primary }}
            >
              ◀️ Back to Other Options
            </Button>

            <View style={styles.registerContainer}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                Don't have an account?{' '}
              </Text>
              <Button
                mode="text"
                onPress={() => router.push('/(auth)/register')}
                style={{ marginLeft: -8 }}
                labelStyle={{ color: theme.colors.primary }}
              >
                Register Now
              </Button>
            </View>
          </View>
        )}
      </Surface>

      <View style={styles.supportSection}>
        <Text
          variant="bodyMedium"
          style={[styles.supportText, { color: theme.colors.onSurfaceVariant }]}
        >
          Need help?
        </Text>
        <View style={styles.supportButtons}>
          <TouchableOpacity
            onPress={handleCallSupport}
            style={[styles.supportButton, { backgroundColor: theme.colors.surfaceVariant }]}
            activeOpacity={0.7}
          >
            <MaterialCommunityIcons
              name="phone"
              size={24}
              color={theme.colors.primary}
              style={styles.supportIcon}
            />
            <Text
              variant="bodyMedium"
              style={[styles.supportButtonText, { color: theme.colors.primary }]}
            >
              Call
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleEmailSupport}
            style={[styles.supportButton, { backgroundColor: theme.colors.surfaceVariant }]}
            activeOpacity={0.7}
          >
            <MaterialCommunityIcons
              name="email"
              size={24}
              color={theme.colors.primary}
              style={styles.supportIcon}
            />
            <Text
              variant="bodyMedium"
              style={[styles.supportButtonText, { color: theme.colors.primary }]}
            >
              Email
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleWhatsAppSupport}
            style={[styles.supportButton, { backgroundColor: theme.colors.surfaceVariant }]}
            activeOpacity={0.7}
          >
            <MaterialCommunityIcons
              name="whatsapp"
              size={24}
              color={theme.colors.primary}
              style={styles.supportIcon}
            />
            <Text
              variant="bodyMedium"
              style={[styles.supportButtonText, { color: theme.colors.primary }]}
            >
              WhatsApp
            </Text>
          </TouchableOpacity>
        </View>
        <Button
          mode="text"
          onPress={() => setIsEmailLogin(true)}
          style={styles.troubleLoginButton}
          labelStyle={{ color: `${theme.colors.onSurfaceVariant}99` }}
        >
          Trouble logging in? Use email instead
        </Button>
      </View>
    </ScrollView>
    // </SafeAreaView>
  );
};
export default LoginScreen;

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flexGrow: 1,
    alignItems: 'center',
    justifyContent: 'space-evenly',
    padding: 24,
    minHeight: height,
  },
  card: {
    width: CARD_WIDTH,
    borderRadius: 24,
    padding: 32,
    alignItems: 'center',
    // marginTop: height * 0.05,
  },
  logoContainer: {
    width: 120,
    height: 100,
    // marginBottom: 8,
  },
  logo: {
    width: '100%',
    height: '100%',
  },
  textContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 52,
  },
  title: {
    fontSize: 28,
    lineHeight: 36,
    marginBottom: 8,
    textAlign: 'center',
    fontWeight: '700',
  },
  titleHighlight: {
    fontWeight: '800',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
  emailLoginContainer: {
    width: '100%',
  },
  input: {
    marginBottom: 16,
    width: '100%',
  },
  loginButton: {
    width: '100%',
    marginBottom: 16,
    borderRadius: 12,
  },
  buttonContent: {
    paddingVertical: 8,
  },
  switchButton: {
    marginTop: 8,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginVertical: 16,
  },
  dividerLine: {
    flex: 1,
    height: 1,
  },
  dividerText: {
    marginHorizontal: 16,
  },
  googleIcon: {
    marginRight: 8,
  },
  supportSection: {
    marginTop: 32,
    alignItems: 'center',
  },
  supportText: {
    marginBottom: 16,
  },
  supportButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
  },
  supportButton: {
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    minWidth: 80,
  },
  supportIcon: {
    marginBottom: 4,
  },
  supportButtonText: {
    marginTop: 4,
    fontSize: 12,
  },
  registerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
  },
  troubleLoginContainer: {
    // flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 24,
    marginBottom: 4,
  },
  troubleLoginButton: {
    marginTop: 12,
    width: '100%',
  },
});
