import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { TextInput, Button, Text, HelperText } from 'react-native-paper';
import { router } from 'expo-router';
import { useSignInEmailPasswordless } from '@nhost/react';

export default function OTPScreen() {
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { signInEmailPasswordless } = useSignInEmailPasswordless();

  const handleVerify = async () => {
    if (!code || code.length < 6) {
      setError('Please enter the verification code from your email');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const { error: verificationError, isSuccess } = await signInEmailPasswordless(code);
      
      if (verificationError) {
        setError(verificationError.message);
      } else if (isSuccess) {
        router.replace('/(main)');
      }
    } catch (err) {
      setError('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text variant="headlineMedium" style={styles.title}>Enter Verification Code</Text>
      <Text variant="bodyMedium" style={styles.subtitle}>
        Enter the verification code sent to your email
      </Text>
      <TextInput
        mode="outlined"
        label="Verification Code"
        value={code}
        onChangeText={(text) => {
          setCode(text.replace(/[^0-9]/g, ''));
          setError('');
        }}
        keyboardType="number-pad"
        maxLength={6}
        style={styles.input}
        disabled={loading}
        error={!!error}
      />
      <HelperText type="error" visible={!!error}>
        {error}
      </HelperText>
      <Button 
        mode="contained" 
        onPress={handleVerify}
        style={styles.button}
        loading={loading}
        disabled={loading || code.length < 6}
      >
        Verify
      </Button>
      <Button 
        mode="text" 
        onPress={() => router.back()}
        style={styles.backButton}
        disabled={loading}
      >
        Back to Email
      </Button>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    marginBottom: 20,
    textAlign: 'center',
    opacity: 0.7,
  },
  input: {
    marginBottom: 8,
  },
  button: {
    padding: 5,
    marginTop: 12,
  },
  backButton: {
    marginTop: 10,
  },
});
