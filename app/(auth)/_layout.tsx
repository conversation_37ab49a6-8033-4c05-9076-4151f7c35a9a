import { Stack } from "expo-router";
import { useAuthenticationStatus } from "@nhost/react";
import { useEffect } from "react";
import { router } from "expo-router";

export default function AuthLayout() {


  return (
    <Stack screenOptions={{
      headerShown: false,
    }}>
      <Stack.Screen
        name="login"
        options={{
          title: "Login",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="register"
        options={{
          title: "Register",
          headerShown: false,
        }}
      />
    </Stack>
  );
}
