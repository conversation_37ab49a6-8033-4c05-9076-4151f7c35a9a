import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  ScrollView,
  Linking,
  Alert,
  Platform,
  Image,
  TouchableOpacity,
} from 'react-native';
import { TextInput, Button, Text, HelperText } from 'react-native-paper';
import { router } from 'expo-router';
import { useSignUpEmailPassword } from '@nhost/react';
import { COLORS, SUPPORT_PHONE, SUPPORT_EMAIL, SUPPORT_WHATSAPP } from '@/constants';

export default function RegisterScreen() {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');

  const {
    signUpEmailPassword,
    isLoading: isLoadingRegister,
    isError,
    error: signUpError,
  } = useSignUpEmailPassword();

  const handleRegister = async () => {
    if (!firstName || !lastName || !email || !password || !confirmPassword) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return;
    }

    try {
      const { error, isSuccess, needsEmailVerification } = await signUpEmailPassword(
        email,
        password,
        {
          displayName: `${firstName} ${lastName}`,
          metadata: {
            firstName,
            lastName,
          },
          redirectTo: 'totoloco://login',
        }
      );

      if (error) {
        // console.error('Error during sign up:', error);
        Alert.alert('Registration Error', error.message);
        return;
      }

      if (isSuccess || needsEmailVerification) {
        Alert.alert(
          'Registration Successful',
          'A verification email has been sent to your email address. Please verify your email to continue.',
          [
            {
              text: 'OK',
              onPress: () => router.replace('/(auth)/login'),
            },
          ]
        );
      }
    } catch (err) {
      Alert.alert('Error', 'An error occurred during registration. Please try again.');
    }
  };

  const handleCallSupport = () => {
    Linking.openURL(`tel:${SUPPORT_PHONE}`);
  };

  const handleEmailSupport = () => {
    Linking.openURL(`mailto:${SUPPORT_EMAIL}`);
  };

  const handleWhatsAppSupport = () => {
    const message = encodeURIComponent('Hello! I need help with registering in the Bolaoo app.');
    Linking.openURL(`whatsapp://send?phone=${SUPPORT_WHATSAPP}&text=${message}`);
  };

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.card}>
          <View style={styles.logoContainer}>
            <Image
              source={require('../../assets/images/splash-icon.png')}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>

          <Text variant="headlineMedium" style={styles.title}>
            Create Account
          </Text>
          <Text variant="bodyLarge" style={styles.subtitle}>
            Sign up to get started
          </Text>

          <View style={styles.form}>
            <View style={styles.row}>
              <TextInput
                mode="outlined"
                label="First Name"
                value={firstName}
                onChangeText={setFirstName}
                style={[styles.input, styles.halfInput]}
              />
              <TextInput
                mode="outlined"
                label="Last Name"
                value={lastName}
                onChangeText={setLastName}
                style={[styles.input, styles.halfInput]}
              />
            </View>

            <TextInput
              mode="outlined"
              label="Email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              style={styles.input}
            />

            <TextInput
              mode="outlined"
              label="Password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
              right={
                <TextInput.Icon
                  icon={showPassword ? 'eye-off' : 'eye'}
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
              style={styles.input}
            />

            <TextInput
              mode="outlined"
              label="Confirm Password"
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry={!showConfirmPassword}
              right={
                <TextInput.Icon
                  icon={showConfirmPassword ? 'eye-off' : 'eye'}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                />
              }
              style={styles.input}
            />

            {error ? (
              <HelperText type="error" visible={!!error}>
                {error}
              </HelperText>
            ) : null}

            <Button
              mode="contained"
              onPress={handleRegister}
              loading={isLoadingRegister}
              disabled={isLoadingRegister}
              style={styles.button}
              buttonColor={COLORS.primary}
              contentStyle={styles.buttonContent}
            >
              Create Account
            </Button>

            <View style={styles.links}>
              <TouchableOpacity onPress={() => router.push('/(auth)/login')} style={styles.link}>
                <Text style={styles.linkText}>Already have an account? Sign in</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.supportContainer}>
              <Text variant="bodySmall" style={styles.supportText}>
                Need help?
              </Text>
              <View style={styles.supportButtons}>
                <Button
                  mode="text"
                  icon="whatsapp"
                  onPress={handleWhatsAppSupport}
                  style={styles.supportButton}
                  textColor={COLORS.disabled}
                >
                  WhatsApp
                </Button>
                <Button
                  mode="text"
                  icon="email"
                  onPress={handleEmailSupport}
                  style={styles.supportButton}
                  textColor={COLORS.disabled}
                >
                  Email
                </Button>
                <Button
                  mode="text"
                  icon="phone"
                  onPress={handleCallSupport}
                  style={styles.supportButton}
                  textColor={COLORS.disabled}
                >
                  Call
                </Button>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  card: {
    padding: 24,
    borderRadius: 12,
    backgroundColor: COLORS.surface,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  logo: {
    width: 120,
    height: 120,
  },
  title: {
    color: COLORS.primary,
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  subtitle: {
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: 32,
    opacity: 0.7,
  },
  form: {
    gap: 16,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  input: {
    backgroundColor: COLORS.surface,
  },
  halfInput: {
    flex: 1,
  },
  button: {
    marginTop: 8,
  },
  buttonContent: {
    paddingVertical: 8,
  },
  links: {
    marginTop: 24,
    gap: 12,
  },
  link: {
    alignItems: 'center',
  },
  linkText: {
    color: COLORS.primary,
    fontSize: 16,
  },
  supportContainer: {
    marginTop: 24,
    alignItems: 'center',
  },
  supportText: {
    color: COLORS.text,
    opacity: 0.7,
  },
  supportButtons: {
    flexDirection: 'row',
    gap: 4,
  },
  supportButton: {
    minWidth: 0,
  },
});
