import { Slot, useRouter } from 'expo-router';
import { View, Text, Image, StyleSheet } from 'react-native';
import { PaperProvider } from 'react-native-paper';
import { ApolloProvider } from '@apollo/client';
import { NhostProvider, useAuthenticationStatus, useUserId } from '@nhost/react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import NetInfo from '@react-native-community/netinfo';
import * as Notifications from 'expo-notifications';
import { initialWindowMetrics, SafeAreaProvider } from 'react-native-safe-area-context';
import * as SplashScreen from 'expo-splash-screen';
import * as Sentry from '@sentry/react-native';
import { isRunningInExpoGo } from 'expo';
import { useFonts } from 'expo-font';
import ScreenWrapper from './components/ScreenWrapper';
import { ThemeProvider } from '../src/theme/ThemeContext';
import { useTheme } from '../src/theme/ThemeContext';
import { LocationProvider } from './contexts/LocationContext';
import { RouteProvider } from './contexts/RouteContext';
import { NotificationProvider } from './contexts/NotificationContext';
import nhost from './utils/nhost';
import { I18nextProvider } from 'react-i18next';
import i18n from './utils/i18n';
import useLocationServices from './hooks/useLocationServices';
import apolloClient from './utils/apollo-client';
import AnimatedLoadingScreen from './components/AnimatedLoadingScreen';
import { env } from '@/config';
import useAppVersion from './hooks/useAppVersion';
import React from 'react';

if (__DEV__) {
  import('../ReactotronConfig');
}

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

// Construct a new integration instance
const navigationIntegration = Sentry.reactNavigationIntegration({
  enableTimeToInitialDisplay: !isRunningInExpoGo(),
});

// Initialize Sentry
Sentry.init({
  dsn: env.DSN, // Replace with your actual Sentry DSN
  debug: false, // Set to false in production
  tracesSampleRate: 1.0, // Adjust this value in production
  integrations: [navigationIntegration],
  enableNativeFramesTracking: !isRunningInExpoGo(),
});

function RootLayoutNav() {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const router = useRouter();
  const { requestPermissions } = useLocationServices();
  const [isConnected, setIsConnected] = useState<boolean | null>(true);
  const { theme } = useTheme();
  const [isValidating, setIsValidating] = useState(true);
  const { t } = useTranslation();
  const userId = useUserId();

  const { versionLoading } = useAppVersion(userId);

  const [fontsLoaded] = useFonts({
    'Poppins-Regular': require('../assets/fonts/Poppins-Regular.ttf'),
    'Poppins-Medium': require('../assets/fonts/Poppins-Medium.ttf'),
    'Poppins-SemiBold': require('../assets/fonts/Poppins-SemiBold.ttf'),
    'Poppins-Bold': require('../assets/fonts/Poppins-Bold.ttf'),
  });

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected);
    });
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    async function initializeApp() {
      try {
        // Clear all notifications when app opens
        await Notifications.dismissAllNotificationsAsync();
        if (!isAuthenticated) {
          await SplashScreen.hideAsync();
          router.replace('/(auth)/login');
          return;
        }

        await SplashScreen.hideAsync();

        const isGranted = await requestPermissions();

        if (!isGranted) {
          router.replace('/(main)/request-permissions');
          return;
        }

        router.replace('/(main)');
      } catch (error) {
        console.error('Error in initialization:', error);
      } finally {
        setIsValidating(false);
      }
    }

    if (!isLoading && fontsLoaded && isConnected) {
      initializeApp();
    }
  }, [isAuthenticated, isLoading, fontsLoaded, isConnected]);

  if (isLoading || !fontsLoaded || isValidating || versionLoading) {
    return (
      <View style={{ flex: 1 }}>
        <View style={{ height: 0 }}>
          <Slot />
        </View>
        <AnimatedLoadingScreen />
      </View>
    );
  }

  if (!isConnected) {
    return (
      <View style={[styles.noInternetContainer, { backgroundColor: theme.colors.background }]}>
        <Image
          source={require('../assets/images/bolaoo-logo.png')}
          style={{ width: 200 }}
          resizeMode="contain"
        />
        <Text style={[styles.noInternetText, { color: theme.colors.error }]}>
          {t('common.offline.title')}!
        </Text>
        <Text style={[styles.noInternetSubText, { color: theme.colors.primary }]}>
          {t('common.offline.message')}
        </Text>
      </View>
    );
  }

  return (
    <ScreenWrapper>
      <Slot />
    </ScreenWrapper>
  );
}

function RootLayout() {
  return (
    <NhostProvider nhost={nhost}>
      <ApolloProvider client={apolloClient}>
        <NotificationProvider>
          <ThemeProvider>
            <LocationProvider>
              <RouteProvider>
                <SafeAreaProvider initialMetrics={initialWindowMetrics}>
                  <PaperProvider
                    theme={{
                      ...useTheme().theme,
                      fonts: {
                        ...useTheme().theme.fonts,
                        displayLarge: { fontFamily: 'Poppins-Bold' },
                        displayMedium: { fontFamily: 'Poppins-Bold' },
                        displaySmall: { fontFamily: 'Poppins-SemiBold' },
                        headlineLarge: { fontFamily: 'Poppins-SemiBold' },
                        headlineMedium: { fontFamily: 'Poppins-SemiBold' },
                        headlineSmall: { fontFamily: 'Poppins-Medium' },
                        titleLarge: { fontFamily: 'Poppins-Medium' },
                        titleMedium: { fontFamily: 'Poppins-Medium' },
                        titleSmall: { fontFamily: 'Poppins-Medium' },
                        bodyLarge: { fontFamily: 'Poppins-Regular' },
                        bodyMedium: { fontFamily: 'Poppins-Regular' },
                        bodySmall: { fontFamily: 'Poppins-Regular' },
                        labelLarge: { fontFamily: 'Poppins-Medium' },
                        labelMedium: { fontFamily: 'Poppins-Medium' },
                        labelSmall: { fontFamily: 'Poppins-Regular' },
                      },
                    }}
                  >
                    <I18nextProvider i18n={i18n}>
                      <RootLayoutNav />
                    </I18nextProvider>
                  </PaperProvider>
                </SafeAreaProvider>
              </RouteProvider>
            </LocationProvider>
          </ThemeProvider>
        </NotificationProvider>
      </ApolloProvider>
    </NhostProvider>
  );
}

// Wrap the Root Layout route component
export default Sentry.wrap(RootLayout);

const styles = StyleSheet.create({
  noInternetContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  noInternetText: {
    fontSize: 32,
    fontFamily: 'Poppins-Bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  noInternetSubText: {
    fontSize: 24,
    fontFamily: 'Poppins-Regular',
    textAlign: 'center',
    opacity: 0.8,
  },
});
