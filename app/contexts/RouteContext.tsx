import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

type RouteContextType = {
  routeGeometry: any[];
  distance: number | null;
  setRouteInfo: (geometry: any[], distance: number) => void;
  clearRouteInfo: () => void;
};

const RouteContext = createContext<RouteContextType | undefined>(undefined);

const ROUTE_GEOMETRY_KEY = '@route_geometry';
const ROUTE_DISTANCE_KEY = '@route_distance';

export const RouteProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [routeGeometry, setRouteGeometry] = useState<any[]>([]);
  const [distance, setDistance] = useState<number | null>(null);

  // Load saved route data on mount
  useEffect(() => {
    const loadSavedRoute = async () => {
      try {
        const [savedGeometry, savedDistance] = await Promise.all([
          AsyncStorage.getItem(ROUTE_GEOMETRY_KEY),
          AsyncStorage.getItem(ROUTE_DISTANCE_KEY)
        ]);

        if (savedGeometry) {
          setRouteGeometry(JSON.parse(savedGeometry));
        }
        if (savedDistance) {
          setDistance(Number(savedDistance));
        }
      } catch (error) {
        console.error('Error loading saved route:', error);
      }
    };

    loadSavedRoute();
  }, []);

  const setRouteInfo = async (geometry: any[], dist: number) => {
    try {
      await Promise.all([
        AsyncStorage.setItem(ROUTE_GEOMETRY_KEY, JSON.stringify(geometry)),
        AsyncStorage.setItem(ROUTE_DISTANCE_KEY, String(dist))
      ]);
      setRouteGeometry(geometry);
      setDistance(dist);
    } catch (error) {
      console.error('Error saving route info:', error);
      // Still update state even if storage fails
      setRouteGeometry(geometry);
      setDistance(dist);
    }
  };

  const clearRouteInfo = async () => {
    try {
      await Promise.all([
        AsyncStorage.removeItem(ROUTE_GEOMETRY_KEY),
        AsyncStorage.removeItem(ROUTE_DISTANCE_KEY)
      ]);
      setRouteGeometry([]);
      setDistance(null);
    } catch (error) {
      console.error('Error clearing route info:', error);
      // Still clear state even if storage fails
      setRouteGeometry([]);
      setDistance(null);
    }
  };

  return (
    <RouteContext.Provider 
      value={{ 
        routeGeometry, 
        distance, 
        setRouteInfo, 
        clearRouteInfo 
      }}
    >
      {children}
    </RouteContext.Provider>
  );
};

export default RouteProvider;

export function useRoute() {
  const context = useContext(RouteContext);
  if (context === undefined) {
    throw new Error('useRoute must be used within a RouteProvider');
  }
  return context;
};
