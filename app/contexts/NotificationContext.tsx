import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import { env } from '@/config';
import { useUserData } from '@nhost/react';
import { useMutation, useApolloClient } from '@apollo/client';
import { UPDATE_USER_PROFILE } from 'app/graphql/nearby';
import { gql } from '@apollo/client';

// Add a query to get the driver's current notification token from server
const GET_PROFILE_NOTIFICATION_TOKEN = gql`
  query GetProfileNotificationToken($profileId: uuid!) {
    user_profiles_by_pk(id: $profileId) {
      notification_token
      metadata
    }
  }
`;

interface NotificationContextType {
  isNotificationsEnabled: boolean;
  pushToken: string | null;
  enableNotifications: () => Promise<void>;
  disableNotifications: () => Promise<void>;
  loadNotificationState: () => Promise<void>;
  sendLocalNotification: (title: string, body: string) => Promise<void>;
  hasPermission: boolean;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

const NOTIFICATIONS_ENABLED_KEY = '@notifications_enabled_passenger';
const PUSH_TOKEN_KEY = '@push_token_passenger';

// Configure notification handler
Notifications.setNotificationHandler({
  handleNotification: async () => {
    try {
      const enabled = await AsyncStorage.getItem(NOTIFICATIONS_ENABLED_KEY);
      const shouldShow = enabled !== 'false'; // Default to showing notifications

      return {
        shouldShowAlert: shouldShow,
        shouldPlaySound: shouldShow,
        shouldSetBadge: shouldShow,
        priority: Notifications.AndroidNotificationPriority.HIGH,
        lockscreenVisibility: Notifications.AndroidNotificationVisibility.PUBLIC,
      };
    } catch (error) {
      console.error('Error in notification handler:', error);
      return {
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
        priority: Notifications.AndroidNotificationPriority.HIGH,
        lockscreenVisibility: Notifications.AndroidNotificationVisibility.PUBLIC,
      };
    }
  },
});

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const [isNotificationsEnabled, setIsNotificationsEnabled] = useState(false);
  const [pushToken, setPushToken] = useState<string | null>(null);
  const [hasPermission, setHasPermission] = useState(false);

  const user = useUserData();
  const [updateUserProfile] = useMutation(UPDATE_USER_PROFILE);
  const client = useApolloClient();

  // Add function to check server token
  const checkServerToken = useCallback(
    async (currentToken: string) => {
      try {
        const profileId = await AsyncStorage.getItem('profileId');
        if (!profileId) return;

        const { data } = await client.query({
          query: GET_PROFILE_NOTIFICATION_TOKEN,
          variables: { profileId },
          fetchPolicy: 'network-only',
        });

        const serverToken = data?.user_profiles_by_pk?.notification_token;
        const serverMetadata = data?.user_profiles_by_pk?.metadata;

        const currentDeviceInfo = {
          platform: Platform.OS,
          version: Platform.Version,
          model: Device.modelName || 'Unknown',
          brand: Device.brand || 'Unknown',
          isDevice: Device.isDevice,
          lastUpdated: new Date().toISOString(),
          notificationsEnabled: true,
        };

        // Compare key device identifiers
        const deviceChanged =
          !serverMetadata ||
          serverMetadata.model !== currentDeviceInfo.model ||
          serverMetadata.brand !== currentDeviceInfo.brand ||
          serverMetadata.platform !== currentDeviceInfo.platform;

        // Only update if token or device has changed
        if (serverToken !== currentToken || deviceChanged) {
          await updateUserProfile({
            variables: {
              id: profileId,
              data: { notification_token: currentToken, metadata: currentDeviceInfo },
            },
          });
        }
      } catch (error) {
        console.error('Error checking server token:', error);
      }
    },
    [client, updateUserProfile]
  );

  const setupNotificationChannel = useCallback(async () => {
    try {
      if (Platform.OS === 'android') {
        const channel = await Notifications.getNotificationChannelAsync('rides_notifications');
        if (channel) return;

        await Notifications.setNotificationChannelAsync('rides_notifications', {
          name: 'Rides',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
          sound: 'notification_sound.wav',
          lockscreenVisibility: Notifications.AndroidNotificationVisibility.PUBLIC,
          enableLights: true,
          enableVibrate: true,
          bypassDnd: true,
          description: 'your ride notifications',
          showBadge: true,
        });
      }
      return true;
    } catch (error) {
      console.error('Error in setupNotificationChannel:', error);
      throw error;
    }
  }, []);

  const updatePushToken = useCallback(
    async (token: string | null) => {
      try {
        const profileId = await AsyncStorage.getItem('profileId');
        if (!profileId) return;

        if (token) {
          const storedToken = await AsyncStorage.getItem(PUSH_TOKEN_KEY);
          if (storedToken !== token) {
            await AsyncStorage.setItem(PUSH_TOKEN_KEY, token);
          }
          await checkServerToken(token);
        } else {
          await AsyncStorage.removeItem(PUSH_TOKEN_KEY);
          await updateUserProfile({
            variables: {
              id: profileId,
              data: { notification_token: null },
            },
          });
        }
      } catch (error) {
        console.error('Error updating push token:', error);
      }
    },
    [checkServerToken, updateUserProfile]
  );

  const registerForPushNotificationsAsync = useCallback(
    async (retries = 3) => {
      if (!Device.isDevice) {
        console.log('Must use physical device for Push Notifications');
        return null;
      }

      try {
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;

        if (existingStatus !== 'granted') {
          const { status } = await Notifications.requestPermissionsAsync({
            ios: {
              allowAlert: true,
              allowBadge: true,
              allowSound: true,
            },
          });
          finalStatus = status;
        }

        if (finalStatus !== 'granted') return null;

        const expoPushToken = await Notifications.getExpoPushTokenAsync({
          projectId: env.EXPO_PROJECT_ID,
        });

        await setupNotificationChannel();

        const token = expoPushToken.data;
        setPushToken(token);
        await AsyncStorage.setItem(PUSH_TOKEN_KEY, token);
        updatePushToken(token);

        return token;
      } catch (error) {
        console.error('Error in registerForPushNotificationsAsync:', error);
        if (retries > 0) {
          await new Promise(resolve => setTimeout(resolve, 1000));
          return registerForPushNotificationsAsync(retries - 1);
        }
        return null;
      }
    },
    [setupNotificationChannel, updatePushToken]
  );

  const loadNotificationState = useCallback(async () => {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      setHasPermission(existingStatus === 'granted');

      const [enabled, storedToken] = await Promise.all([
        AsyncStorage.getItem(NOTIFICATIONS_ENABLED_KEY),
        AsyncStorage.getItem(PUSH_TOKEN_KEY),
      ]);

      if (storedToken) {
        setPushToken(storedToken);
        setIsNotificationsEnabled(true);
        await checkServerToken(storedToken);
      }

      if (enabled === null || enabled === 'true') {
        const token = await registerForPushNotificationsAsync();
        if (token && token !== storedToken) {
          setPushToken(token);
          setIsNotificationsEnabled(true);
          await Promise.all([
            AsyncStorage.setItem(NOTIFICATIONS_ENABLED_KEY, 'true'),
            AsyncStorage.setItem(PUSH_TOKEN_KEY, token),
            checkServerToken(token),
          ]);
        }
      }
    } catch (error) {
      console.error('Error loading notification state:', error);
      setIsNotificationsEnabled(false);
      setPushToken(null);
      setHasPermission(false);
    }
  }, [checkServerToken, registerForPushNotificationsAsync]);

  const enableNotifications = useCallback(async () => {
    try {
      const token = await registerForPushNotificationsAsync();
      if (token) {
        await Promise.all([
          AsyncStorage.setItem(NOTIFICATIONS_ENABLED_KEY, 'true'),
          updatePushToken(token),
        ]);
        setPushToken(token);
        setIsNotificationsEnabled(true);
      }
    } catch (error) {
      console.error('Error enabling notifications:', error);
    }
  }, [registerForPushNotificationsAsync, updatePushToken]);

  const disableNotifications = useCallback(async () => {
    try {
      await Promise.all([
        AsyncStorage.setItem(NOTIFICATIONS_ENABLED_KEY, 'false'),
        AsyncStorage.removeItem(PUSH_TOKEN_KEY),
        Notifications.dismissAllNotificationsAsync(),
        Notifications.cancelAllScheduledNotificationsAsync(),
      ]);

      if (Platform.OS === 'android') {
        await Notifications.deleteNotificationChannelAsync('rides_notifications');
      }

      setIsNotificationsEnabled(false);
      setPushToken(null);
      await updatePushToken(null);
    } catch (error) {
      console.error('Error in disableNotifications:', error);
    }
  }, [updatePushToken]);

  const sendLocalNotification = useCallback(
    async (title: string, body: string) => {
      if (!isNotificationsEnabled) return;

      try {
        await Notifications.scheduleNotificationAsync({
          content: {
            title,
            body,
            sound: true,
            priority: Notifications.AndroidNotificationPriority.HIGH,
            vibrate: [0, 250, 250, 250],
            categoryIdentifier: 'rides_notifications',
          },
          trigger: null,
        });
      } catch (error) {
        console.error('Error sending local notification:', error);
      }
    },
    [isNotificationsEnabled]
  );

  useEffect(() => {
    let isMounted = true;

    const initialize = async () => {
      if (isMounted) {
        await loadNotificationState();
      }
    };

    const subscription = Notifications.addNotificationReceivedListener(notification => {
      if (isMounted) {
        // Handle received notification
        // console.log('Notification received:', notification);
      }
    });

    const responseSubscription = Notifications.addNotificationResponseReceivedListener(response => {
      if (isMounted) {
        // Handle notification response
        // console.log('Notification response:', response);
      }
    });

    initialize();

    return () => {
      isMounted = false;
      subscription.remove();
      responseSubscription.remove();
    };
  }, []);

  return (
    <NotificationContext.Provider
      value={{
        isNotificationsEnabled,
        pushToken,
        enableNotifications,
        disableNotifications,
        loadNotificationState,
        sendLocalNotification,
        hasPermission,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}

export default NotificationProvider;
