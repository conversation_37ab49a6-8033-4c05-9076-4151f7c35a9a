import { SUPPORT_PHONE, SUPPORT_EMAIL, SUPPORT_WHATSAPP } from '@/constants';
import { Linking, Alert, Platform } from 'react-native';

export const handleCallSupport = async () => {
  const phoneUrl = `tel:${SUPPORT_PHONE}`;
  try {
    const supported = await Linking.canOpenURL(phoneUrl);
    if (supported) {
      await Linking.openURL(phoneUrl);
    } else {
      Alert.alert(
        'Phone Call Not Supported',
        'Your device does not support making phone calls. Please try another support method.',
        [{ text: 'OK' }]
      );
    }
  } catch (error) {
    // console.error('Error making phone call:', error);
    Alert.alert('Error', 'Could not initiate phone call. Please try another support method.');
  }
};

export const handleEmailSupport = async () => {
  const emailUrl = `mailto:${SUPPORT_EMAIL}`;
  try {
    const supported = await Linking.canOpenURL(emailUrl);
    if (supported) {
      await Linking.openURL(emailUrl);
    } else {
      Alert.alert(
        'Email Not Supported',
        'Your device does not have an email app configured. Please try another support method.',
        [{ text: 'OK' }]
      );
    }
  } catch (error) {
    // console.error('Error opening email:', error);
    Alert.alert('Error', 'Could not open email. Please try another support method.');
  }
};

export const handleWhatsAppSupport = async () => {
  const message = 'Hello! I need support with the Bolaoo app.';
  const phoneNumber = SUPPORT_WHATSAPP.replace('+', ''); // Remove + from the phone number
  const waUrl = `whatsapp://send?phone=${phoneNumber}&text=${encodeURIComponent(message)}`;

  try {
    // Directly try to open WhatsApp
    await Linking.openURL(waUrl);
  } catch (error) {
    // console.error('Error opening WhatsApp:', error);

    // If direct WhatsApp URL fails, try web URL as fallback
    const webUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    try {
      await Linking.openURL(webUrl);
    } catch (webError) {
      // console.error('Error opening web URL:', webError);
      const playStoreUrl = 'https://play.google.com/store/apps/details?id=com.whatsapp';
      const appStoreUrl = 'https://apps.apple.com/app/whatsapp-messenger/id310633997';

      Alert.alert('WhatsApp Error', 'Could not open WhatsApp. Would you like to install it?', [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Install WhatsApp',
          onPress: () => Linking.openURL(Platform.OS === 'ios' ? appStoreUrl : playStoreUrl),
        },
      ]);
    }
  }
};

export default {};
