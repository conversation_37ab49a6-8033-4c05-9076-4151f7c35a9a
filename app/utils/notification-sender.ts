import { EXPO_NOTIFICATION_BASE_URL } from '@/constants';
import axios from 'axios';
import * as Notifications from 'expo-notifications';

export const sendtoDriverNotification = async (
  driverPushToken: string,
  title: string,
  body: string
) => {
  if (!driverPushToken) return;

  try {
    const response = await axios.post(
      EXPO_NOTIFICATION_BASE_URL,
      {
        to: driverPushToken,
        title,
        body,
        sound: 'notification_sound.wav',
        priority: Notifications.AndroidNotificationPriority.HIGH,
        channelId: 'rides_notifications',
        showInForeground: true,
        playSound: true,
        sticky: true,
        // Android specific
        android: {
          sound: 'notification_sound.wav',
          priority: 'high',
          vibrate: [0, 250, 250, 250],
          channelId: 'rides_notifications',
          showInForeground: true,
          playSound: true,
          sticky: true,
        },
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    console.error('Error sending notification:', error);
  }
};

export default {};
