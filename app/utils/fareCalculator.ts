export const calculateFare = (distanceInKm: number): number => {
  if (distanceInKm <= 0) return 0;
  
  // Round up the distance to the next kilometer
  const roundedDistance = Math.ceil(distanceInKm);
  
  // First kilometer costs Rs. 30
  let totalFare = 30;
  
  // Additional kilometers cost Rs. 20 each
  if (roundedDistance > 1) {
    totalFare += (roundedDistance - 1) * 20;
  }
  
  return totalFare;
};

export default calculateFare;
