import { NhostClient } from '@nhost/nhost-js';
import { env } from '../../src/config';
import AsyncStorage from '@react-native-async-storage/async-storage';

// const nhost = new NhostClient({
//   authUrl: `https://${env.NHOST.AUTH_URL}/v1`,
//   graphqlUrl: `https://${env.NHOST.SUBDOMAIN}.graphql.${env.NHOST.REGION}.nhost.run/v1`,
//   functionsUrl: `https://${env.NHOST.SUBDOMAIN}.functions.${env.NHOST.REGION}.nhost.run/v1`,
//   storageUrl: `https://${env.NHOST.SUBDOMAIN}.storage.${env.NHOST.REGION}.nhost.run/v1`,
//   clientStorageType: 'react-native',
//   clientStorage: AsyncStorage,
//   autoSignIn: true,
//   autoRefreshToken: true,
// });

const nhost = new NhostClient({
  region: 'ap-south-1',
  subdomain: 'mnxpyzclitwjvzyfksvh',
  clientStorageType: 'react-native',
  clientStorage: AsyncStorage,
  autoSignIn: true,
  autoRefreshToken: true,
});

export default nhost;
