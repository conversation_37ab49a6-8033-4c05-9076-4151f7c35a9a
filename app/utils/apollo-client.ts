import { RetryLink } from '@apollo/client/link/retry';
import { setContext } from '@apollo/client/link/context';
import {
  createHttpLink,
  ApolloClient,
  InMemoryCache,
  split,
  from,
  Observable,
  NormalizedCacheObject,
} from '@apollo/client';
import { onError } from '@apollo/client/link/error';
import { createClient } from 'graphql-ws';
import { GraphQLWsLink } from '@apollo/client/link/subscriptions';
import { getMainDefinition } from '@apollo/client/utilities';
import nhost from './nhost';

const retryLink = new RetryLink({
  attempts: {
    max: 5,
    retryIf: (error: any) => !!error,
  },
});

const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  if (graphQLErrors) {
    for (const err of graphQLErrors) {
      if (err.extensions?.code === 'invalid-jwt') {
        return new Observable(observer => {
          nhost.auth
            .refreshSession()
            .then(() => {
              const subscriber = {
                next: observer.next.bind(observer),
                error: observer.error.bind(observer),
                complete: observer.complete.bind(observer),
              };
              forward(operation).subscribe(subscriber);
            })
            .catch((error: Error) => {
              // console.error('Failed to refresh token:', error);
              observer.error(error);
            });
        });
      }
    }
  }
  if (networkError) {
    // console.error('Network Error:', networkError);
  }
});

const cache = new InMemoryCache({
  typePolicies: {
    Query: {
      fields: {
        free_rides: {
          merge(existing = [], incoming) {
            return incoming;
          },
        },
        driver: {
          merge: true,
        },
      },
    },
    Subscription: {
      fields: {
        free_rides: {
          merge(existing = [], incoming) {
            return incoming;
          },
        },
      },
    },
  },
});

const httpLink = createHttpLink({
  uri: nhost.graphql.httpUrl,
});

const wsClient = createClient({
  url: nhost.graphql.wsUrl,
  connectionParams: async () => {
    let retries = 0;
    const maxRetries = 3;
    const retryDelay = 1000; // 1 second

    while (retries < maxRetries) {
      try {
        const token = await nhost.auth.getAccessToken();
        if (!token) {
          console.log(`Attempt ${retries + 1}/${maxRetries}: Waiting for access token...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          retries++;
          continue;
        }

        // console.log('Access token obtained successfully');
        return {
          headers: {
            authorization: `Bearer ${token}`,
          },
        };
      } catch (error) {
        // console.error(
        //   `Attempt ${retries + 1}/${maxRetries}: Failed to get connection parameters:`,
        //   error
        // );
        if (retries === maxRetries - 1) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        retries++;
      }
    }
    throw new Error('Failed to obtain access token after maximum retries');
  },
  shouldRetry: (errOrCloseEvent: unknown) => {
    // Handle token-related errors
    if (
      errOrCloseEvent instanceof Error &&
      errOrCloseEvent.message.includes('No access token available')
    ) {
      // console.error('WebSocket auth error: No access token available');
      return true;
    }

    // Handle null message errors - likely connection interruption
    if (
      errOrCloseEvent &&
      typeof errOrCloseEvent === 'object' &&
      'message' in errOrCloseEvent &&
      errOrCloseEvent.message === null &&
      'isTrusted' in errOrCloseEvent
    ) {
      return true;
    }

    if (errOrCloseEvent instanceof Error) {
      // console.error('WebSocket unexpected error:', errOrCloseEvent.message);
      return true;
    }

    // Check if it's a close event-like object (has code property)
    if (errOrCloseEvent && typeof errOrCloseEvent === 'object' && 'code' in errOrCloseEvent) {
      const closeEvent = errOrCloseEvent as { code: number; reason?: string };
      // Don't retry for authentication errors
      const authErrorCodes = [4001, 4004, 4403];
      if (authErrorCodes.includes(closeEvent.code)) {
        // console.error(`WebSocket auth error: Code ${closeEvent.code}`);
        return false;
      }
      return true;
    }

    // console.error('WebSocket unknown error type');
    return true;
  },
  retryAttempts: 5,
  retryWait: async retries => {
    const baseDelay = 1000;
    const maxDelay = 10000;
    const delay = Math.min(baseDelay * Math.pow(2, retries), maxDelay);
    return new Promise(resolve => setTimeout(resolve, delay));
  },
  // keepAlive: 20000,
  // connectionAckWaitTimeout: 10000,
  // lazy: false,
  // on: {
  //   connected: () => {
  //     console.log("WebSocket connected successfully");
  //   },
  //   error: (err) => {
  //     // Only log JWT-related errors as they indicate configuration issues
  //     if (err && typeof err === "object" && "message" in err) {
  //       const errorMessage = String(err.message);
  //       if (errorMessage.includes("JWT") || errorMessage.includes("jwt")) {
  //         console.error("JWT Authentication Error:", errorMessage);
  //       }
  //     }
  //   },
  //   closed: () => {
  //     console.log("WebSocket connection closed");
  //   },
  //   connecting: () => {
  //     console.log("WebSocket connecting...");
  //   },
  //   opened: () => {
  //     console.log("WebSocket connection opened");
  //   },
  // },
});

const wsLink = new GraphQLWsLink(wsClient);

const authLink = setContext(async (_, { headers }) => {
  const token = await nhost.auth.getAccessToken();
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : '',
    },
  };
});

const splitLink = split(
  ({ query }) => {
    const definition = getMainDefinition(query);
    return definition.kind === 'OperationDefinition' && definition.operation === 'subscription';
  },
  wsLink,
  httpLink
);

const apolloClient = new ApolloClient<NormalizedCacheObject>({
  link: from([errorLink, retryLink, authLink, splitLink]),
  cache,
  defaultOptions: {
    watchQuery: {
      fetchPolicy: 'cache-and-network',
      nextFetchPolicy: 'cache-first',
      errorPolicy: 'all',
      notifyOnNetworkStatusChange: true,
    },
    query: {
      fetchPolicy: 'cache-first',
      errorPolicy: 'all',
      notifyOnNetworkStatusChange: true,
    },
    mutate: {
      errorPolicy: 'all',
    },
  },
  connectToDevTools: true,
});

export default apolloClient;
