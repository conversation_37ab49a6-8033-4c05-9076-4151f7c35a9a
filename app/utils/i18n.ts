import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import * as Localization from 'expo-localization';
import AsyncStorage from '@react-native-async-storage/async-storage';

import enTranslations from '../locales/en.json';
import neTranslations from '../locales/ne.json';

const resources = {
  en: { translation: enTranslations },
  ne: { translation: neTranslations }
};

const LANGUAGE_STORAGE_KEY = 'app_language';

const getInitialLanguage = async () => {
  try {
    // First, check if user has previously selected a language
    const storedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
    if (storedLanguage) return storedLanguage;

    // If not, use device's preferred language
    const deviceLanguage = Localization.locale.split('-')[0];
    return ['en', 'ne'].includes(deviceLanguage) ? deviceLanguage : 'en';
  } catch (error) {
    console.error('Error getting initial language:', error);
    return 'en';
  }
};

export const changeLanguage = async (languageCode: string) => {
  if (['en', 'ne'].includes(languageCode)) {
    await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, languageCode);
    await i18n.changeLanguage(languageCode);
  }
};

export const getCurrentLanguage = () => {
  return i18n.language || 'en';
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    compatibilityJSON: 'v4',
    fallbackLng: 'en',
    lng: 'en',
    interpolation: {
      escapeValue: false, // react already escapes values
    },
    ns: ['translation'],
    defaultNS: 'translation',
  } as any);

// Set initial language on app start
getInitialLanguage().then((language) => {
  i18n.changeLanguage(language);
});

export default i18n;
