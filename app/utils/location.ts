import { env } from "@/config";
import { LOCATIONIQ_BASE_URL } from "@/constants";
import axios from "axios";

export const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const d = R * c; // Distance in km
  return d;
};

export const deg2rad = (deg: number): number => {
  return deg * (Math.PI / 180);
};





export const formatDistance = (kilometers: number) => {
  if (kilometers < 1) {
    return `${Math.round(kilometers*1000)}m`;
  }
  return `${(kilometers).toFixed(1)}km`;
};



export const getPickupAddress = async (latitude: number, longitude: number) => {
  try {
    if (!latitude || !longitude) {
      return 'Unknown Location';
    }

    const response = await axios.get(
      `${LOCATIONIQ_BASE_URL}/reverse?key=${env.LOCATIONIQ_KEY}&lat=${latitude}&lon=${longitude}&format=json`
    );

    // Extract a readable address, preferring display_name for full details
    return response.data.display_name || 'Unknown Location';
  } catch (error) {
    console.error('Error fetching pickup address:', error);
    return 'Unable to fetch address';
  }
};




const locationUtils = {
  calculateDistance,
  deg2rad,
};





export default locationUtils;
